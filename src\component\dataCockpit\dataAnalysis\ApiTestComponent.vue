<template>
  <div class="api-test-container">
    <h2>API连接测试</h2>
    
    <div class="test-section">
      <h3>1. 基础连接测试</h3>
      <el-button type="primary" @click="testBasicConnection" :loading="loading">
        测试API连接
      </el-button>
      
      <div v-if="testResult" class="test-result">
        <h4>测试结果:</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 参数测试</h3>
      <div class="param-inputs">
        <el-input v-model="testParams.startDate" placeholder="开始日期 YYYY-MM-DD" />
        <el-input v-model="testParams.endDate" placeholder="结束日期 YYYY-MM-DD" />
        <el-input v-model="testParams.colorCode" placeholder="色号" />
      </div>
      <el-button type="success" @click="testWithParams" :loading="loading">
        测试带参数请求
      </el-button>
    </div>

    <div class="test-section">
      <h3>3. 错误日志</h3>
      <div v-if="errorLog.length > 0" class="error-log">
        <div v-for="(error, index) in errorLog" :key="index" class="error-item">
          <strong>{{ error.time }}:</strong> {{ error.message }}
        </div>
      </div>
      <div v-else class="no-errors">暂无错误</div>
    </div>

    <div class="test-section">
      <h3>4. 网络检查</h3>
      <el-button type="warning" @click="checkNetwork">检查网络连接</el-button>
      <div v-if="networkStatus" class="network-status">
        网络状态: {{ networkStatus }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAnalysisData } from '@/api/dataAnalysis/dyeingAnalysis'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const loading = ref(false)
const testResult = ref(null)
const errorLog = ref([])
const networkStatus = ref('')

const testParams = ref({
  startDate: '2025-01-01',
  endDate: '2025-01-31',
  colorCode: ''
})

// 添加错误到日志
const addError = (message) => {
  errorLog.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (errorLog.value.length > 10) {
    errorLog.value = errorLog.value.slice(0, 10)
  }
}

// 测试基础连接
const testBasicConnection = async () => {
  loading.value = true
  testResult.value = null
  
  try {
    console.log('开始测试API连接...')
    
    const response = await getAnalysisData({
      startDate: '2025-01-01',
      endDate: '2025-01-31'
    })
    
    testResult.value = {
      success: true,
      code: response.code,
      dataCount: response.data?.length || 0,
      sampleData: response.data?.slice(0, 2) || [],
      fullResponse: response
    }
    
    ElMessage.success(`API调用成功！返回${response.data?.length || 0}条数据`)
    
  } catch (error) {
    console.error('API测试失败:', error)
    
    testResult.value = {
      success: false,
      error: error.message,
      details: error
    }
    
    addError(`API调用失败: ${error.message}`)
    ElMessage.error(`API调用失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 测试带参数的请求
const testWithParams = async () => {
  loading.value = true
  testResult.value = null
  
  try {
    console.log('测试带参数的API调用...', testParams.value)
    
    const response = await getAnalysisData(testParams.value)
    
    testResult.value = {
      success: true,
      params: testParams.value,
      code: response.code,
      dataCount: response.data?.length || 0,
      sampleData: response.data?.slice(0, 2) || []
    }
    
    ElMessage.success(`参数测试成功！返回${response.data?.length || 0}条数据`)
    
  } catch (error) {
    console.error('参数测试失败:', error)
    
    testResult.value = {
      success: false,
      params: testParams.value,
      error: error.message
    }
    
    addError(`参数测试失败: ${error.message}`)
    ElMessage.error(`参数测试失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 检查网络连接
const checkNetwork = async () => {
  try {
    // 检查基础网络连接
    const response = await fetch('http://223.95.171.10:18090/api/data/invoke/1/analysis-data/0?api_secret=DRyEnruYbgYOGbiw', {
      method: 'GET',
      mode: 'cors'
    })
    
    if (response.ok) {
      networkStatus.value = '网络连接正常'
      ElMessage.success('网络连接正常')
    } else {
      networkStatus.value = `网络连接异常: ${response.status} ${response.statusText}`
      ElMessage.warning('网络连接异常')
    }
  } catch (error) {
    networkStatus.value = `网络连接失败: ${error.message}`
    addError(`网络检查失败: ${error.message}`)
    ElMessage.error('网络连接失败')
  }
}
</script>

<style scoped lang="scss">
.api-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  
  h3 {
    margin-top: 0;
    color: #333;
  }
}

.param-inputs {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
  
  .el-input {
    flex: 1;
    min-width: 200px;
  }
}

.test-result {
  margin-top: 15px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
  
  pre {
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 300px;
    overflow-y: auto;
  }
}

.error-log {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  background: #fff5f5;
  
  .error-item {
    margin-bottom: 5px;
    color: #d63031;
    font-size: 14px;
  }
}

.no-errors {
  color: #00b894;
  font-style: italic;
}

.network-status {
  margin-top: 10px;
  padding: 10px;
  background: #e8f4fd;
  border-radius: 4px;
  color: #0984e3;
}
</style>
