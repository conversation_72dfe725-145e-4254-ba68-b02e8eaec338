<template>
  <div class="analysis-group">
    <!-- 图表展示区 -->
    <div class="charts-row">
      <!-- TOP15分析图表 -->
      <Top15Chart
        :title="top15Title"
        :subtitle="top15Subtitle"
        :chart-data="top15Data"
        :no-data-message="top15NoDataMessage"
        @chart-click="handleTop15Click"
      />

      <!-- 组成分析图表 -->
      <div class="composition-analysis-container">
        <!-- 色号面料组成分析 - 仅用于色号分析 -->
        <ColorFabricAnalysisChart
          v-if="dataType === '色号'"
          :title="colorFabricTitle"
          :selected-color="selectedColorForFabricAnalysis"
          :fabric-analysis-data="colorFabricData"
          :fabric-type="currentFabricType"
          :no-data-message="colorFabricNoDataMessage"
          @toggle-fabric-type="handleToggleFabricType"
        />

        <!-- 面料色号组成分析 - 用于面料分析 -->
        <FabricColorAnalysisChart
          v-else
          :title="fabricColorTitle"
          :selected-fabric="selectedFabricForColorAnalysis"
          :color-analysis-data="fabricColorData"
          :no-data-message="fabricColorNoDataMessage"
        />
      </div>
    </div>

    <!-- 月度分析图表 - 单独一行 -->
    <div class="monthly-analysis-row">
      <MonthlyAnalysisChart
        :title="monthlyTitle"
        :data-type="dataType"
        :selected-item="selectedItemForMonthly"
        :placeholder="monthlyPlaceholder"
        :monthly-data="monthlyDataForSelected"
        :no-data-message="monthlyNoDataMessage"
      />
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- TOP15筛选 -->
      <div class="control-group">
        <div class="group-title">TOP15筛选</div>
        <div class="filter-controls">
          <el-date-picker
            v-model="localStartDate"
            type="date"
            placeholder="开始日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
          />
          <el-date-picker
            v-model="localEndDate"
            type="date"
            placeholder="结束日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
          />
          <div class="tank-type-filter">
            <el-button
              :type="localTankType === 'all' ? 'primary' : 'default'"
              @click="setTankType('all')"
            >
              全部
            </el-button>
            <el-button
              :type="localTankType === 'large' ? 'primary' : 'default'"
              @click="setTankType('large')"
            >
              大缸(≥250)
            </el-button>
            <el-button
              :type="localTankType === 'small' ? 'primary' : 'default'"
              @click="setTankType('small')"
            >
              小缸(&lt;250)
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import ColorFabricAnalysisChart from './ColorFabricAnalysisChart.vue';
import FabricColorAnalysisChart from './FabricColorAnalysisChart.vue';
import MonthlyAnalysisChart from './MonthlyAnalysisChart.vue';
import Top15Chart from './Top15Chart.vue';

const props = defineProps({
  // 原始数据
  rawData: {
    type: Array,
    default: () => []
  },

  // TOP15相关
  top15Title: {
    type: String,
    required: true
  },
  top15Subtitle: {
    type: String,
    default: '按统计回修率和染色缸数'
  },
  top15Data: {
    type: Object,
    default: () => ({ items: [], batches: [], repairRates: [] })
  },
  top15NoDataMessage: {
    type: String,
    default: '没有符合的数据'
  },

  // 月度分析相关
  monthlyTitle: {
    type: String,
    required: true
  },
  dataType: {
    type: String,
    required: true
  },
  monthlyPlaceholder: {
    type: String,
    default: '请点击上方图表选择项目'
  },
  monthlyNoDataMessage: {
    type: String,
    default: '请点击上方图表选择项目进行月度分析'
  },

  // 数据字段名
  dataField: {
    type: String,
    required: true
  },

  // 初始值
  startDate: String,
  endDate: String,
  tankType: {
    type: String,
    default: 'all'
  }
});

// 计算属性 - 色号面料组成分析相关
const colorFabricTitle = computed(() => {
  return '色号面料组成分析';
});

const colorFabricNoDataMessage = computed(() => {
  return `请点击左侧的色号图表查看面料组成分析`;
});

// 计算属性 - 面料色号组成分析相关
const fabricColorTitle = computed(() => {
  return props.dataType === '面料' ? '面料色号组成分析' : '项目色号组成分析';
});

const fabricColorNoDataMessage = computed(() => {
  return `请点击左侧的${props.top15Title}查看色号组成分析`;
});

const emit = defineEmits(['update:startDate', 'update:endDate', 'update:tankType']);

// 本地状态
const localStartDate = ref(props.startDate);
const localEndDate = ref(props.endDate);
const localTankType = ref(props.tankType);

// 色号面料分析状态
const selectedColorForFabricAnalysis = ref('');
const colorFabricData = ref([]);
const currentFabricType = ref('decathlon'); // 'decathlon' | 'dewei'

// 面料色号分析状态
const selectedFabricForColorAnalysis = ref('');
const fabricColorData = ref([]);

// 月度分析状态
const selectedItemForMonthly = ref('');
const monthlyDataForSelected = ref([]);

// 监听props变化
watch(() => props.startDate, (val) => localStartDate.value = val);
watch(() => props.endDate, (val) => localEndDate.value = val);
watch(() => props.tankType, (val) => localTankType.value = val);

// 监听本地状态变化并发射事件
watch(localStartDate, (val) => emit('update:startDate', val));
watch(localEndDate, (val) => emit('update:endDate', val));
watch(localTankType, (val) => emit('update:tankType', val));

// 设置缸型筛选
const setTankType = (type) => {
  localTankType.value = type;
};



// 处理TOP15图表点击事件 - 根据数据类型使用不同逻辑
const handleTop15Click = (itemName) => {
  if (!itemName || !props.rawData.length) {
    return;
  }

  // 更新月度分析的选中项目
  selectedItemForMonthly.value = itemName;
  analyzeMonthlyData(itemName);

  if (props.dataType === '色号') {
    // 色号分析：点击色号显示面料组成
    selectedColorForFabricAnalysis.value = itemName;
    analyzeColorFabrics(itemName);
  } else {
    // 面料分析：点击面料显示色号组成
    selectedFabricForColorAnalysis.value = itemName;
    analyzeFabricColors(itemName);
  }
};

// 分析色号的面料组成
const analyzeColorFabrics = (colorCode) => {
  // 过滤出该色号的所有数据
  const colorData = props.rawData.filter(item => {
    const itemColorCode = item.colorCode || '未知色号';
    return itemColorCode === colorCode;
  });

  if (colorData.length === 0) {
    colorFabricData.value = [];
    return;
  }

  // 根据当前面料类型选择字段
  const fabricField = currentFabricType.value === 'decathlon' ? 'decathlonFabric' : 'deweiFabric';

  // 按面料统计
  const fabricStats = {};
  colorData.forEach(item => {
    const fabricName = item[fabricField] || '未知面料';

    if (!fabricStats[fabricName]) {
      fabricStats[fabricName] = { total: 0, repair: 0 };
    }

    fabricStats[fabricName].total += 1;
    if (item.result === 0) {
      fabricStats[fabricName].repair += 1;
    }
  });

  // 转换为数组并计算回修率
  const fabricArray = Object.entries(fabricStats).map(([fabricName, stats]) => ({
    fabricName,
    batches: stats.total,
    repairCount: stats.repair,
    repairRate: stats.total > 0 ? parseFloat(((stats.repair / stats.total) * 100).toFixed(1)) : 0
  }));

  if (fabricArray.length === 0) {
    colorFabricData.value = [];
    return;
  }

  // 取TOP15（按总缸数排序）
  const top15 = [...fabricArray]
    .sort((a, b) => b.batches - a.batches)
    .slice(0, 15);

  // 按回修率从高到低排序
  const sortedByRepairRate = [...top15]
    .sort((a, b) => b.repairRate - a.repairRate);

  colorFabricData.value = sortedByRepairRate;
};

// 分析面料的色号组成
const analyzeFabricColors = (fabricName) => {
  // 过滤出该面料的所有数据
  const fabricData = props.rawData.filter(item => {
    const fabricField = props.dataField;
    return item[fabricField] && item[fabricField] === fabricName;
  });

  if (fabricData.length === 0) {
    fabricColorData.value = [];
    return;
  }

  // 按色号统计
  const colorStats = {};
  fabricData.forEach(item => {
    const colorCode = item.colorCode || '未知色号';

    if (!colorStats[colorCode]) {
      colorStats[colorCode] = { total: 0, repair: 0 };
    }

    colorStats[colorCode].total += 1;
    if (item.result === 0) {
      colorStats[colorCode].repair += 1;
    }
  });

  // 转换为数组并计算回修率
  const colorArray = Object.entries(colorStats).map(([colorCode, stats]) => ({
    colorCode,
    batches: stats.total,
    repairCount: stats.repair,
    repairRate: stats.total > 0 ? parseFloat(((stats.repair / stats.total) * 100).toFixed(1)) : 0
  }));

  if (colorArray.length === 0) {
    fabricColorData.value = [];
    return;
  }

  // 取TOP15（按总缸数排序）
  const top15 = [...colorArray]
    .sort((a, b) => b.batches - a.batches)
    .slice(0, 15);

  // 按回修率从高到低排序
  const sortedByRepairRate = [...top15]
    .sort((a, b) => b.repairRate - a.repairRate);

  fabricColorData.value = sortedByRepairRate;
};

// 分析月度数据
const analyzeMonthlyData = (itemName) => {
  // 根据数据类型选择筛选字段
  const filterField = props.dataType === '色号' ? 'colorCode' : props.dataField;

  // 过滤出该项目的所有数据
  const itemData = props.rawData.filter(item => {
    const itemValue = item[filterField] || (props.dataType === '色号' ? '未知色号' : '未知面料');
    return itemValue === itemName;
  });

  if (itemData.length === 0) {
    monthlyDataForSelected.value = [];
    return;
  }

  // 按月份统计
  const monthlyStats = {};
  itemData.forEach(item => {
    if (!item.dyeDate) return;

    const month = item.dyeDate.substring(0, 7); // YYYY-MM格式

    if (!monthlyStats[month]) {
      monthlyStats[month] = { total: 0, repair: 0 };
    }

    monthlyStats[month].total += 1;
    if (item.result === 0) {
      monthlyStats[month].repair += 1;
    }
  });

  // 转换为数组并计算回修率
  const monthlyArray = Object.entries(monthlyStats).map(([month, stats]) => ({
    month,
    batches: stats.total,
    repairCount: stats.repair,
    repairRate: stats.total > 0 ? parseFloat(((stats.repair / stats.total) * 100).toFixed(1)) : 0
  }));

  // 按月份排序
  monthlyArray.sort((a, b) => a.month.localeCompare(b.month));

  monthlyDataForSelected.value = monthlyArray;
};

// 处理面料类型切换
const handleToggleFabricType = (fabricType) => {
  currentFabricType.value = fabricType;

  // 如果有选中的色号，重新分析
  if (selectedColorForFabricAnalysis.value) {
    analyzeColorFabrics(selectedColorForFabricAnalysis.value);
  }
};
</script>

<style scoped lang="scss">
.analysis-group {
  margin-bottom: 40px;
}

.charts-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin-bottom: 30px;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
}

.composition-analysis-container {
  .chart-card {
    width: 100%;
  }
}

.monthly-analysis-row {
  margin-bottom: 30px;

  .chart-card {
    width: 100%;
  }
}

.control-panel {
  background: #ffffff;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;

  .control-group {
    max-width: 600px;
    width: 100%;
  }
  
  .control-group {
    .group-title {
      font-size: 16px;
      font-weight: bold;
      color: #1a1a1a;
      margin-bottom: 18px;
      padding-bottom: 10px;
      border-bottom: 2px solid #3366ff;
    }
    
    .filter-controls {
      display: flex;
      gap: 12px;
      margin-bottom: 18px;
      flex-wrap: wrap;
      
      .el-date-editor {
        flex: 1;
        min-width: 160px;

        :deep(.el-input__inner) {
          border-radius: 8px;
        }
      }

      .tank-type-filter {
        display: flex;
        gap: 8px;

        .el-button {
          min-width: 80px;
          font-size: 12px;
          padding: 8px 16px;

          &.el-button--default {
            background: #f5f7fa;
            color: #606266;
            border: 1px solid #dcdfe6;

            &:hover {
              background: #ecf5ff;
              color: #409eff;
              border-color: #c6e2ff;
            }
          }
        }
      }

      .el-button {
        flex-shrink: 0;
        background: linear-gradient(135deg, #3366ff, #294cff);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(41, 76, 255, 0.25);
          transform: translateY(-2px);
        }
      }
    }

  }
}
</style>
