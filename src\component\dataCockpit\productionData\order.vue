<template>
    <div>
        <div class="title-style" :style="{ backgroundColor: titleColor }">
            <h3>{{ title }}</h3>
        </div>
        <table class="container" border="1" cellspacing="0" cellpadding="5">
            <thead>
                <tr>
                    <th>{{ smallTitle.firstTitle }}</th>
                    <th>{{ smallTitle.secondTitle }}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="total">{{ data.one }}</td>
                    <td style="font-weight: bolder;font-size: large;">{{ data.two }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { DataProps } from './interface';
const props = defineProps({
    title: {
        type: String,
        required: false,
        default: '一、接单情况'
    },
    data: {
        type: Object as PropType<DataProps>,
        required: false,
        default: () => ({})
    },
    titleColor: {
        type: String,
        required: false,

    },
    smallTitle: {
        type: Object as PropType<{
            firstTitle: string;
            secondTitle: string;
        }>,
        required: false,
        default: () => ({})
    }

})

const title = props.title ?? '一、接单情况';
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>