<template>
    <div id="main">
        <el-select v-model="selectedDept" placeholder="选择部门" clearable style="width: 200px; margin-bottom: 20px;">
            <el-option label="全部部门" value=""></el-option>
            <el-option v-for="dept in departments" :key="dept" :label="dept" :value="dept" />
        </el-select>

        <el-row :gutter="20" class="project-list">
            <el-col v-for="project in pagedProjects" :key="project.id" :span="6" class="project-col">
                <el-card shadow="hover" class="project-card">
                    <a :href="project.url" target="_blank" rel="noopener noreferrer">
                        <img :src="project.preview" alt="预览图" class="project-img" />
                    </a>
                    <div class="project-title">{{ project.title }}</div>
                </el-card>
            </el-col>
        </el-row>

        <div class="pagination-controls" style="margin-top: 20px; display: flex; align-items: center; gap: 20px;">
            <el-select v-model="pageSize" placeholder="每页条数" style="width: 120px;">
                <el-option v-for="size in pageSizeOptions" :key="size" :label="size + ' 条/页'" :value="size" />
            </el-select>

            <el-pagination background layout="prev, pager, next" :page-size="pageSize" :total="filteredProjects.length"
                v-model:current-page="currentPage" />
        </div>
    </div>
</template>

<script setup lang="ts" name="dataCockpit">
import dataCockpitImg from '@/assets/dataCockpit.png'
import { computed, ref, watch } from 'vue'
interface Project {
    id: number
    department: string
    title: string
    preview: string
    url: string
}




const projects = ref<Project[]>([
    { id: 1, department: '人事课', title: '人力资源管理驾驶舱', preview: dataCockpitImg, url: '/#/hr1' },
    // 新增数据分析看板条目
    { id: 2, department: '迪卡侬', title: '迪卡侬数据分析看板', preview: dataCockpitImg, url: '/#/dataAnalysis' },
    { id: 3, department: '化验室', title: '染色追加分析看板', preview: dataCockpitImg, url: '/#/dyeingAppendAnalysis' }
])

const selectedDept = ref<string>('')

const departments = computed(() => {
    const depts = new Set(projects.value.map(p => p.department))
    return Array.from(depts)
})

const filteredProjects = computed(() => {
    if (!selectedDept.value) return projects.value
    return projects.value.filter(p => p.department === selectedDept.value)
})

const pageSizeOptions = [12, 24, 48]

const pageSize = ref<number>(12)
const currentPage = ref(1)

const pagedProjects = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    return filteredProjects.value.slice(start, start + pageSize.value)
})

// 当部门或每页条数变化时，重置当前页为1
watch([selectedDept, pageSize], () => {
    currentPage.value = 1
})
</script>

<style scoped lang="scss">
#main {
    width: 100vw;
    height: 80vh;
    padding: 20px;
    box-sizing: border-box;
    background: #f5f7fa;
    overflow-y: auto;

}

.project-list {
    justify-content: flex-start;
    /* 左对齐 */
}

.project-col {
    margin-top: 10px;
    margin-bottom: 10px;
}

.project-card {
    cursor: pointer;
    transition: transform 0.2s ease;

    .project-img {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 4px 4px 0 0;
        display: block;
    }

    .project-title {
        margin-top: 10px;
        font-size: 1.1rem;
        color: #333;
        text-align: left;
    }

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
}

.pagination-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #fff;
    /* 可选，防止遮挡内容时背景透明 */
    padding: 10px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    z-index: 1000;
    /* 确保在最上层 */
    display: flex;
    align-items: center;
    gap: 20px;
}
</style>