import service from '@/request/index'
/**
 * 
 * @param beginTime 
 * @param endTime 
 * @returns 
 */
const productionData = async (beginTime, endTime) => {
    return await service({
        url: "/api/DataCenter/GetSGAllData",
        method: "get",
        params: {
            beginTime,
            endTime
        }
    }).then((val) => {
        return val
    }).catch((error) => {

        return error
    })
}
/**
 * 
 * @param beginTime 
 * @param endTime 
 * @returns 
 */
const shipmentAmount = async (startTime, endTime, type) => {
    return await service({
        url: "/api/DataCenter/GetZCHJE",
        method: "get",
        params: {
            startTime: startTime,
            endTime,
            type
        }
    }).then((val) => {
        return val
    }).catch((error) => {

        return error
    })
}
export {
    productionData,
    shipmentAmount
}