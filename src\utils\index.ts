import { h } from 'vue'
import { ElMessage } from 'element-plus'


/**
 * @param {date} time 需要转换的时间
 * @param {String} fmt 需要转换的格式 如 yyyy-MM-dd、yyyy-MM-dd HH:mm:ss
 * @returns {String}
 */
const formatTime = (
    time: string | number | Date,
    fmt: string
): string => {
    if (!time) return ''
    const date = new Date(time)
    const o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'H+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        S: date.getMilliseconds(),
    }
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(
            RegExp.$1,
            (date.getFullYear() + '').substr(4 - RegExp.$1.length)
        )
    for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                // @ts-ignore: Unreachable code error
                RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
            )
        }
    }
    return fmt
}
/**
 * @param {object} date 后端返回的访问类型 

 * @returns {Array} 分组合并结果
 */
const splitList = (data: any): Array<object> => {
    // 初始化结果对象
    const result: any = {};
    // 遍历 response 数组
    data.data.response.forEach((item: any) => {
        // 如果 MainId 不存在，则初始化为一个空数组
        if (!result[item.MainId]) {
            result[item.MainId] = [];
        }
        // 将对象添加到相应的 MainId 数组中
        result[item.MainId].push(item);
    });
    // 对每个分类的数组根据 SortNo 排序
    for (let key in result) {
        result[key].sort((a: { SortNo: number }, b: { SortNo: number }) => a.SortNo - b.SortNo);
    }
    // 动态生成结果数组
    const res = [];
    for (let key in result) {
        const temp = result[key].map((element: { Id: any; Name: any }) => ({
            value: element.Id,
            label: element.Name
        }));
        res.push(temp);
    }
    // 输出结果
    return res;
}


function extractAndCombineNumbers(str: string) {
    // 使用正则表达式匹配数字
    const numbers = str.match(/\d+/g);
    // 如果没有找到数字，返回空字符串
    return numbers ? numbers.join('') : '';
}
const getParamValue = (param) => {
    const url = new URL(window.location.href);
    return url.searchParams.get(param);
}
/**
 * 
 * @param func 
 * @param delay
 * 防抖功能 
 */
function debounce(func, delay) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}
export { getParamValue, formatTime, splitList, extractAndCombineNumbers, debounce }

import router from '@/router'; // 确保已导出 router 实例

/**
 * 通用退出登录方法
 * @function logout
 * @description 
 * 1. 清除本地认证信息（token 和 tokenTime）
 * 2. 跳转至登录页（无历史记录，使用 replace 导航）
 */
export function logout(): void {
    // 清除认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('tokenTime');

    // 跳转登录页（使用路由名称避免硬编码路径）
    router.replace({ name: 'login' });
}
