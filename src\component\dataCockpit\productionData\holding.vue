<template>
    <div>
        <div class="title-style" :style="{ backgroundColor: titleColor }">
            <h3>{{ title }}</h3>
        </div>
        <table class="container" border="1" cellspacing="0" cellpadding="5">
            <thead>
                <tr>
                    <th>{{ smallTitle.firstTitle }}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="font-weight: bolder;font-size: large;" class="total">{{ data.one }}</td>
                </tr>
                <tr>
                    <td class="I">{{ data.two }}</td>
                </tr>
                <tr>
                    <td class="II">{{ data.three }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { DataProps } from './interface';
const props = defineProps({
    title: {
        type: String,
        required: false,
        default: '二、持单情况'
    },
    smallTitle: {
        type: Object,
        required: false,
        default: {
            firstTitle: '',
        }
    },
    data: {
        type: Object as PropType<DataProps>,
        required: false,
        default: () => ({})
    },
    titleColor: {
        type: String,
        required: true,

    }

})
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>