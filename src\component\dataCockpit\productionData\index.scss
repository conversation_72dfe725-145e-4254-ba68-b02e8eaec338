.container {
    width: 100%;
}

h3 {
    margin: 0 0 10px 0;
    /* 减少标题和表格间距 */
}

table {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
    
}

th,
td {
    border: 1px solid #000000;
    padding: 8px;
    position: relative;
    text-align: center;
}
h2 {
  text-align: center;

  color: white;
}

.main{
    line-height: 40px;
    margin: 10px 0;
    border: 1px solid #000000;
}
.title-style{
    line-height: 30px;
    padding-top: 5px;
    border: 1px solid #000000;
}



.total::before {
    content: "总量:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}

.I::before {
    content: "I类:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}

.II::before {
    content: "II类:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}


.ShipmentAmountAll::before{
    content: "合计出货金额:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}
.ShipmentAmountRz::before {
    content: "染整出货金额:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}
.ShipmentAmountTh::before {
    content: "贴合出货金额:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}
.ShipmentAmountPb::before {
    content: "胚布出货金额:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}





.need2L::before {
    content: "2L:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}
.need3L::before {
    content: "3L:";
    position: absolute;
    font-weight: bold;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size:small;
}