<template>
    <div id="index" ref="appRef">
        <div style="position: absolute;background-color: white;z-index: 999;">
            <el-button @click="show = !show">配置</el-button>
            <config @update="showConfig" v-show="show">

            </config>
        </div>
        <div class="bg">
            <dv-loading v-if="loading">Loading...</dv-loading>
            <div v-else class="host-body">
                <div class="d-flex jc-center">
                    <dv-decoration-10 class="dv-dec-10" />
                    <div class="d-flex jc-center">
                        <dv-decoration-8 class="dv-dec-8" :color="decorationColors" />
                        <div class="title">
                            <span class="title-text">{{ title }}</span>
                            <dv-decoration-6 class="dv-dec-6" :reverse="true" :color="['#50e3c2', '#67a1e5']" />
                        </div>
                        <dv-decoration-8 class="dv-dec-8" :reverse="true" :color="decorationColors" />
                    </div>
                    <dv-decoration-10 class="dv-dec-10-s" />
                </div>

                <!-- 第二行 -->
                <div class="d-flex jc-between px-2">
                    <div class="d-flex aside-width">
                        <div class="react-left ml-4 react-l-s">
                            <span class="react-before"></span>
                            <span class="text">{{ subtitle[0] }}</span>
                        </div>
                        <div class="react-left ml-3">
                            <span class="text">{{ subtitle[1] }}</span>
                        </div>
                    </div>
                    <div class="d-flex aside-width">
                        <div class="react-right bg-color-blue mr-3">
                            <span class="text fw-b">{{ subtitle[2] }}</span>
                        </div>
                        <div class="react-right mr-4 react-l-s">
                            <span class="react-after"></span>
                            <span class="text">
                                {{ timeInfo.dateYear }} {{ timeInfo.dateWeek }}
                                {{ timeInfo.dateDay }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="body-box">
                    <!-- 第三行数据 -->
                    <div class="content-box">
                        <div>
                            <dv-border-box-13>
                                <Echarts :option="seniority" :height="380">
                                    <template v-slot:icon>
                                        <icon class="iconfont icon-tongji4"></icon>
                                    </template>
                                    <template v-slot:title>
                                        <div class=" title">司龄
                                        </div>
                                    </template>
                                    <template v-slot:style>
                                        <dv-decoration-3 class="style" />
                                    </template>

                                </Echarts>
                            </dv-border-box-13>
                        </div>
                        <div>
                            <dv-border-box-13>
                                <Echarts :option="age" :height="380">
                                    <template v-slot:icon>
                                        <icon class="iconfont icon-layer-group"></icon>
                                    </template>
                                    <template v-slot:title>
                                        <div class="title">年龄</div>
                                    </template>
                                    <template v-slot:style>
                                        <dv-decoration-1 class="style" />
                                    </template>
                                </Echarts>
                            </dv-border-box-13>
                        </div>
                        <!-- 中间 -->
                        <div>
                            <count :options3="education" :options4="marriage" :titleData="titleData"></count>
                        </div>
                        <!-- 中间 -->
                        <div>
                            <dv-border-box-13>
                                <Echarts :option="leaveOption" :height="380">
                                    <template v-slot:icon>
                                        <icon class="iconfont icon-tongji4"></icon>
                                    </template>
                                    <template v-slot:title>
                                        <div class=" title">离职工龄
                                        </div>
                                    </template>
                                    <template v-slot:style>
                                        <dv-decoration-3 class="style" />
                                    </template>

                                </Echarts>
                            </dv-border-box-13>
                        </div>
                        <div>
                            <dv-border-box-13>

                                <rank2></rank2>



                            </dv-border-box-13>
                        </div>
                    </div>

                    <!-- 第四行数据 -->
                    <div class="bototm-box">
                        <dv-border-box-12>
                            <Echarts :option="demo" :height="380" :width="1000">
                                <template v-slot:icon>
                                    <i class="iconfont icon-vector" />
                                </template>
                                <template v-slot:title>
                                    <div class=" title">人员结构
                                    </div>
                                </template>
                                <template v-slot:style>
                                    <dv-decoration-1 class="style" />
                                </template>
                            </Echarts>
                        </dv-border-box-12>
                        <dv-border-box-12>
                            <Echarts :option="resignationAnalysis" :height="380" :width="1000">
                                <template v-slot:icon>
                                    <i class="iconfont icon-vector" />
                                </template>
                                <template v-slot:title>
                                    <div class=" title">员工离职率
                                    </div>
                                </template>
                                <template v-slot:style>
                                    <dv-decoration-1 class="style" />
                                </template>
                            </Echarts>
                        </dv-border-box-12>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="hrData">
import config from './config.vue'
import Echarts from '@/echarts/index.vue'
import * as echarts from 'echarts'
import useDraw from '@/utils/useDraw'
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import { formatTime } from '@/utils/index'
import { title, subtitle, WEEK } from '@/constant/index'
import count from '@/component/dataCockpit/hr/index.vue'
import rank2 from '@/echarts/rank2/index.vue'
// * 适配处理
const { appRef, calcRate, windowDraw, unWindowDraw } = useDraw()
const titleData = ref([
    {
        number: 0,
        text: '今年累计人数'
    },
    {
        number: 0,
        text: '当月入职人数'
    },
    {
        number: 0,
        text: '当月离职人数'
    },
    {
        number: 0,
        text: '编制数'
    },
    {
        number: 0,
        text: '年度入职人数'
    },
    {
        number: 0,
        text: '年度离职人数'
    },
])
const resignRateData = ref({})
const show = ref(false)
const val = reactive({
    xData: [],
    seriesData: [],
    ageXData: [],
    ageSeriesData: [],
    leaveXData: [],
    leaveSeriesData: [],
    educationSeriesData: [],
    marriageSeriesData: []
})
// * 颜色
const decorationColors = ['#568aea', '#000000']
// * 加载标识
const loading = ref<boolean>(true)
// * 时间内容
const timeInfo = reactive({
    setInterval: 0,
    dateDay: '',
    dateYear: '',
    dateWeek: ''
})
const barData = ref([
    4600,
    5000,
    5500,
    6500,
    7500,
    8500,
    9900,
    12500,
    14000,
    21500,
    23200,
    24450,
    25250,
    33300,
    4600,
    5000,
    5500,
    6500,
    7500,
    8500,
    9900,
    22500,
    14000,
    21500,
    8500,
    9900,
    12500,
    14000,
    21500,
    23200,
    24450,
    25250,
    7500
]);
const lineData = ref([
    18092,
    20728,
    24045,
    28348,
    32808,
    36097,
    39867,
    44715,
    48444,
    50415,
    56061,
    62677,
    59521,
    67560,
    18092,
    20728,
    24045,
    28348,
    32808,
    36097,
    39867,
    44715,
    48444,
    50415,
    36097,
    39867,
    44715,
    48444,
    50415,
    50061,
    32677,
    49521,
    32808
]);
/**
 * 离职工龄数据
 */
const leaveOption = computed(() => {
    return {
        grid: {
            top: '1%',
            bottom: '1%',
            left: '1%',
            right: '1%'
        },
        color: [
            '#37a2da',
            '#32c5e9',
            '#9fe6b8',
            '#ffdb5c',
            '#ff9f7f',
            '#fb7293',
            '#e7bcf3',
            '#8378ea'
        ],
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        toolbox: {
            show: true
        },
        calculable: true,
        legend: {
            orient: 'horizontal',
            icon: 'circle',
            bottom: 0,
            x: 'center',
            data: val.leaveXData,
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '司龄统计',
                type: 'pie',
                radius: [15, 70],
                // roseType: 'area',
                center: ['50%', '40%'],
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: true,
                    color: "#fff",
                },

                data: val.leaveSeriesData
            }
        ]
    }
})
const rateData = computed(() => {
    const result = [];
    const len = Math.min(barData.value.length, lineData.value.length);
    for (let i = 0; i < len; i++) {
        if (lineData.value[i] === 0) {
            result.push(0);
        } else {
            const rate = barData.value[i] / lineData.value[i];
            result.push(parseFloat(rate.toFixed(2)));
        }
    }
    return result;
});
/**
 * 演示数据
 */
const demo = computed(() => {
    return ((val = demoData) => {
        return {
            tooltip: {
                show: true,
                trigger: "item",
                axisPointer: {
                    type: "shadow",
                    label: {
                        show: true,
                        backgroundColor: "#7B7DDC"
                    }
                }
            },
            legend: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },

            grid: {
                x: "8%",
                width: "78%",
                top: "5%",
                bottom: '7%',
                left: '10%',
                right: '10%'
            },
            xAxis: {
                data: val.category,
                axisLine: {
                    lineStyle: {
                        color: "#B4B4B4"
                    }
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: [
                {
                    splitLine: { show: false },
                    axisLine: {
                        lineStyle: {
                            color: "#B4B4B4"
                        }
                    },

                    axisLabel: {
                        formatter: "{value} "
                    }
                },
                {
                    splitLine: { show: false },
                    axisLine: {
                        lineStyle: {
                            color: "#B4B4B4"
                        }
                    },
                    axisLabel: {
                        formatter: "{value} "
                    }
                }
            ],
            series: [
                {
                    name: "数据1",
                    type: "line",
                    smooth: true,
                    showAllSymbol: true,
                    symbol: "emptyCircle",
                    symbolSize: 8,
                    yAxisIndex: 1,
                    itemStyle: {
                        normal: {
                            color: "#F02FC2"
                        }
                    },
                    data: val.rateData
                },
                {
                    name: "数据2",
                    type: "bar",
                    barWidth: 10,
                    itemStyle: {
                        normal: {
                            barBorderRadius: 5,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "#956FD4" },
                                { offset: 1, color: "#3EACE5" }
                            ])
                        }
                    },
                    data: val.barData
                },
                {
                    name: "数据3",
                    type: "bar",
                    barGap: "-100%",
                    barWidth: 10,
                    itemStyle: {
                        normal: {
                            barBorderRadius: 5,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(156,107,211,0.8)" },
                                { offset: 0.2, color: "rgba(156,107,211,0.5)" },
                                { offset: 1, color: "rgba(156,107,211,0.2)" }
                            ])
                        }
                    },
                    z: -12,
                    data: val.lineData
                }
            ]
        }
    })()
})
/**
 * 演示表格样式
 */
const demoData = reactive({
    category: Array.from({ length: 31 }, (_, i) => i + 1),
    barData: barData.value,
    lineData: lineData.value,
    rateData: rateData.value
});
/**
 * 司龄
 */
const seniority = computed(() => {
    return {
        grid: {
            top: '1%',
            bottom: '1%',
            left: '1%',
            right: '1%'
        },
        color: [
            '#37a2da',
            '#32c5e9',
            '#9fe6b8',
            '#ffdb5c',
            '#ff9f7f',
            '#fb7293',
            '#e7bcf3',
            '#8378ea'
        ],
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        toolbox: {
            show: true
        },
        calculable: true,
        legend: {
            orient: 'horizontal',
            icon: 'circle',
            bottom: 0,
            x: 'center',
            data: val.xData,
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '司龄统计',
                type: 'pie',
                radius: [15, 70],
                // roseType: 'area',
                center: ['50%', '40%'],
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: true,
                    color: "#fff",
                },

                data: val.seriesData
            }
        ]
    }
})
/**
 * 离职分析
 */
const resignationAnalysis = computed(() => {
    return resignRateData.value
})
/**
 * 年龄分析
 */
const age = computed(() => {
    return {
        grid: {
            top: '1%',
            bottom: '1%',
            left: '1%',
            right: '1%'
        },
        color: [
            '#37a2da',
            '#32c5e9',
            '#9fe6b8',
            '#ffdb5c',
            '#ff9f7f',
            '#fb7293',
            '#e7bcf3',
            '#8378ea'
        ],
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        toolbox: {
            show: true
        },
        calculable: true,
        legend: {
            orient: 'horizontal',
            icon: 'circle',
            bottom: 0,
            x: 'center',
            data: val.ageXData,
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '年龄统计',
                type: 'pie',
                radius: [15, 70],
                // roseType: 'area',
                center: ['50%', '40%'],
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: true,
                    color: "#fff",
                },

                data: val.ageSeriesData
            }
        ]
    }
})
/**
 * 教育
 */
const education = computed(() => {
    return {
        grid: {
            top: '1%',
            bottom: '1%',
            left: '1%',
            right: '1%'
        },
        color: [
            '#37a2da',
            '#32c5e9',
            '#9fe6b8',
            '#ffdb5c',
            '#ff9f7f',
            '#fb7293',
            '#e7bcf3',
            '#8378ea'
        ],
        tooltip: {
            trigger: 'item',
            show: false,

        },

        series: [
            {
                name: '学历',
                type: 'pie',
                radius: [30, 38],
                center: ['50%', '52%'],
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: false,
                    position: 'center'
                },
                labelLine: {
                    show: false // 关闭引导线
                },
                data: val.educationSeriesData,
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 20,
                        color: "white"
                    }
                },


            }
        ]
    }
})
/**
 * 婚姻
 */
const marriage = computed(() => {
    return {
        grid: {
            top: '1%',
            bottom: '1%',
            left: '1%',
            right: '1%'
        },
        color: [
            '#37a2da',
            '#32c5e9',
            '#9fe6b8',
            '#ffdb5c',
            '#ff9f7f',
            '#fb7293',
            '#e7bcf3',
            '#8378ea'
        ],
        tooltip: {
            trigger: 'item',
            show: false
        },

        series: [
            {
                name: '婚育',
                type: 'pie',
                radius: [30, 38],

                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 20,
                        color: "white"
                    }
                },
                labelLine: {
                    show: false
                },
                data: val.marriageSeriesData,
            }
        ]
    }
})
const showConfig = (data) => {
    // console.log(data.val);
    val.xData = data.val.seniorityAnalysis.xAxis.data
    val.seriesData = data.val.seniorityAnalysis.series[0].data
    val.ageXData = data.val.ageAnalysis.xAxis.data
    val.ageSeriesData = data.val.ageAnalysis.series[0].data
    val.leaveXData = data.val.resignSeniorityAnalysis.xAxis.data
    val.leaveSeriesData = data.val.resignSeniorityAnalysis.series[0].data
    val.educationSeriesData = data.val.educationAnalysis.series[0].data
    val.marriageSeriesData = data.val.marriageAnalysis.series[0].data
    resignRateData.value = generateOptionWithDistinctLegendAndColor(data.val.resignRateAnalysis, echarts)
    console.log(resignRateData.value);
    titleData.value = [
        {
            number: data.val.companyEmployees.total,
            text: '今年累计人数'
        },
        {
            number: data.val.companyEmployees.entryNumberThisMonth,
            text: '当月入职人数'
        },
        {
            number: data.val.companyEmployees.resignNumberThisMonth,
            text: '当月离职人数'
        },
        {
            number: data.val.companyEmployees.headcount,
            text: '编制数'
        },
        {
            number: data.val.companyEmployees.entryNumberThisYear,
            text: '年度入职人数'
        },
        {
            number: data.val.companyEmployees.resignNumberThisYear,
            text: '年度离职人数'
        },
    ]

}
/**
 * 生成渐变色函数
 * @param {string[]} colorArr - 颜色数组，长度为2
 * @returns {echarts.graphic.LinearGradient} 渐变色对象
 */
const getGradientColor = (colorArr: string[]) => {
    return new echarts.graphic.LinearGradient(0, 1, 0, 0, [
        { offset: 0, color: colorArr[0] },
        { offset: 1, color: colorArr[1] }
    ])
}
const generateOptionWithDistinctLegendAndColor = (data, echarts) => {
    const { timeDimension, yearDimension } = data;

    // 科技感蓝紫色渐变色
    const colors = {
        '月度入职人数': ['#00c6ff', '#0072ff'], // 青蓝渐变
        '月度离职人数': ['#7f00ff', '#e100ff'], // 紫色渐变
        '月度离职率': ['#00fff7', '#00c2ff'], // 青色渐变
        '年度入职人数': ['#1a2a6c', '#b21f1f'], // 深蓝到红紫渐变
        '年度离职人数': ['#6a11cb', '#2575fc'], // 紫蓝渐变
        '年度离职率': ['#00d2ff', '#3a47d5']  // 蓝紫渐变
    };

    const selected = {
        '月度入职人数': true,
        '月度离职人数': true,
        '月度离职率': true,
        '年度入职人数': false,
        '年度离职人数': false,
        '年度离职率': false
    };

    const legendData = [
        '月度入职人数', '月度离职人数', '月度离职率',
        '年度入职人数', '年度离职人数', '年度离职率'
    ];

    const series = [
        {
            ...timeDimension.series[0],
            name: '月度入职人数',
            xAxisIndex: 0,
            yAxisIndex: 1,
            itemStyle: {
                color: getGradientColor(colors['月度入职人数']),
                shadowColor: colors['月度入职人数'][1],
                shadowBlur: 15
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 25,
                    shadowColor: '#00ffff'
                }
            },
            z: 2,
            animationEasing: 'elasticOut',
            animationDuration: 1200
        },
        {
            ...timeDimension.series[1],
            name: '月度离职人数',
            xAxisIndex: 0,
            yAxisIndex: 1,
            itemStyle: {
                color: getGradientColor(colors['月度离职人数']),
                shadowColor: colors['月度离职人数'][1],
                shadowBlur: 15
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 25,
                    shadowColor: '#ff00ff'
                }
            },
            z: 2,
            animationEasing: 'elasticOut',
            animationDuration: 1200
        },
        {
            ...timeDimension.series[2],
            name: '月度离职率',
            xAxisIndex: 0,
            yAxisIndex: 0,
            itemStyle: {
                color: getGradientColor(colors['月度离职率']),
                shadowColor: colors['月度离职率'][1],
                shadowBlur: 15
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 25,
                    shadowColor: '#00ffff'
                }
            },
            z: 2,
            animationEasing: 'elasticOut',
            animationDuration: 1200
        },
        {
            ...yearDimension.series[0],
            name: '年度入职人数',
            xAxisIndex: 1,
            yAxisIndex: 1,
            itemStyle: {
                color: getGradientColor(colors['年度入职人数']),
                shadowColor: colors['年度入职人数'][1],
                shadowBlur: 10
            },
            z: 1,
            animationEasing: 'elasticOut',
            animationDuration: 1200
        },
        {
            ...yearDimension.series[1],
            name: '年度离职人数',
            xAxisIndex: 1,
            yAxisIndex: 1,
            itemStyle: {
                color: getGradientColor(colors['年度离职人数']),
                shadowColor: colors['年度离职人数'][1],
                shadowBlur: 10
            },
            z: 1,
            animationEasing: 'elasticOut',
            animationDuration: 1200
        },
        {
            ...yearDimension.series[2],
            name: '年度离职率',
            xAxisIndex: 1,
            yAxisIndex: 0,
            itemStyle: {
                color: getGradientColor(colors['年度离职率']),
                shadowColor: colors['年度离职率'][1],
                shadowBlur: 10
            },
            z: 1,
            animationEasing: 'elasticOut',
            animationDuration: 1200
        }
    ];

    return {
        // backgroundColor: '#0b1d51', // 深蓝色背景
        grid: {
            x: "8%",
            width: "78%",
            top: "15%",
            bottom: '7%',
            left: '10%',
            right: '10%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(50,50,50,0.7)',
            textStyle: { color: '#00ffff' },
            borderColor: '#00ffff',
            borderWidth: 1,
            extraCssText: 'box-shadow: 0 0 10px #00ffff;'
        },
        legend: {
            selected,
            data: legendData,
            textStyle: {
                color: '#00ffff',
                fontWeight: 'bold',
                textShadowColor: '#00ffff',
                textShadowBlur: 10
            },

            borderRadius: 5,
            padding: 5
        },
        xAxis: [
            {
                ...timeDimension.xAxis,
                position: 'bottom',
                axisLine: {
                    onZero: false,
                    lineStyle: { color: '#00ffff', width: 2 }
                },
                axisLabel: {
                    color: '#00ffff',
                    fontWeight: 'bold',
                    fontSize: 12,
                    formatter: value => value // 可自定义格式
                },
                splitLine: {
                    show: false
                }
            },
            {
                ...yearDimension.xAxis,
                position: 'top',
                axisLine: {
                    onZero: false,
                    lineStyle: { color: '#00ffff', width: 2 }
                },
                axisLabel: {
                    color: '#00ffff',
                    fontWeight: 'bold',
                    fontSize: 12
                },
                splitLine: {
                    show: false
                }
            }
        ],
        yAxis: [
            {
                ...timeDimension.yAxis[1], // 离职率轴
                position: 'left',
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(0,255,255,0.2)',
                        type: 'dashed'
                    }
                },
                axisLabel: {
                    formatter: '{value}%',
                    color: '#00ffff',
                    fontWeight: 'bold'
                },
                axisLine: {
                    onZero: false,
                    lineStyle: { color: '#00ffff', width: 2, }
                },
                min: 0,
                max: 50
            },
            {
                ...timeDimension.yAxis[0], // 人数轴
                position: 'right',
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(0,255,255,0.2)',
                        type: 'dashed'
                    }
                },
                axisLabel: {
                    color: '#00ffff',
                    fontWeight: 'bold'
                },
                axisLine: {
                    onZero: false,
                    lineStyle: { color: '#00ffff', width: 2, }
                }
            }
        ],
        series
    };
}
// todo 处理 loading 展示
const cancelLoading = () => {
    setTimeout(() => {
        loading.value = false
    }, 500)
}
// todo 处理时间监听
const handleTime = () => {
    timeInfo.setInterval = setInterval(() => {
        const date = new Date()
        timeInfo.dateDay = formatTime(date, 'HH: mm: ss')
        timeInfo.dateYear = formatTime(date, 'yyyy-MM-dd')
        timeInfo.dateWeek = WEEK[date.getDay()]
    }, 1000)
}
// 生命周期
onMounted(() => {
    cancelLoading()
    handleTime()
    // todo 屏幕适应
    setTimeout(() => {
        calcRate()
        windowDraw()
    }, 0);
})
onUnmounted(() => {
    unWindowDraw()
    clearInterval(timeInfo.setInterval)
})
</script>
<style scoped lang="scss">
@import '@/mobileView/product/dataCockpit/detail/hr/index.scss'
</style>