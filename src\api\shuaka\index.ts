import service from '@/request/index'
/**
 * 获取工卡和物料信息
 * @param {sring} gkh - 工卡号
 * @returns {void} 无返回值
 */
const gkInfo = async (gkh: string) => {
    return await service({
        url: `/api/Product/gkCheck?gkh=${gkh}`,
        method: "get",
        timeout: 5000
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
/**
 * 根据工序编号获取员工清单和机台清单
 * @param {string} gxbh - 工序编号
 * @returns {void}  无返回值
 * 
 */
const GetWorkerAndEquipmentByProcedure = async (gxbh: string) => {
    return await service({
        url: `/api/Product/GetWorkerAndEquipmentByProcedure?gxbh=${gxbh}`,
        method: "get",
        timeout: 5000
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
/**
 * 报工
 * @param data - 报工数据
 * @returns 
 */
const gkTrack = async (data: any) => {
    return await service({
        url: `/api/Product/gkTrack`,
        method: "post",
        data: data,
        timeout: 5000
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}

export { gkInfo, GetWorkerAndEquipmentByProcedure, gkTrack }