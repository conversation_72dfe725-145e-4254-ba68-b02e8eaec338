@import "./variables";

//  全局样式
* {
    margin: 0;
    padding: 0;
    list-style-type: none;
    box-sizing: border-box;
    outline: none;
}

html {
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.2em;
    background-color: #f1f1f1;
    margin: 0;
    padding: 0;
    overflow: auto;
}

a {
    color: #ffffff;
    text-decoration: none;
    box-sizing: border-box;
}

.clearfix {
    &::after {
        content: "";
        display: table;
        height: 0;
        line-height: 0;
        visibility: hidden;
        clear: both;
    }
}

// 图标
.iconfont {
    font-size: 20px !important;
    color: #5cd9e8;
}

//浮动
.float-r {
    float: right;
}

//浮动
.float-l {
    float: left;
}

// 字体加粗
.fw-b {
    font-weight: bold;
}

//文章一行显示，多余省略号显示
.title-item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bg-color-black {
    background-color: rgba(19, 25, 47, 0.6);
}

.bg-color-blue {
    background-color: #1a5cd7;
}

.colorBlack {
    color: #272727 !important;

    &:hover {
        color: #272727 !important;
    }
}

.colorGrass {
    color: #33cea0;

    &:hover {
        color: #33cea0 !important;
    }
}

.colorRed {
    color: #ff5722;

    &:hover {
        color: #ff5722 !important;
    }
}

.colorText {
    color: #d3d6dd !important;

    &:hover {
        color: #d3d6dd !important;
    }
}

.colorBlue {
    color: #257dff !important;

    &:hover {
        color: #257dff !important;
    }
}

//颜色
@each $colorkey,
$color in $colors {
    .text-#{$colorkey} {
        color: $color;
    }

    .bg-#{$colorkey} {
        background-color: $color;
    }
}

//对齐
@each $var in (left, center, right) {
    .text-#{$var} {
        text-align: $var !important;
    }
}

//flex
@each $key,
$value in $flex-jc {
    .jc-#{$key} {
        justify-content: $value;
    }
}

@each $key,
$value in $flex-ai {
    .ai-#{$key} {
        align-items: $value;
    }
}

//字体
@each $fontkey,
$fontvalue in $font-sizes {
    .fs-#{$fontkey} {
        font-size: $fontvalue * $base-font-size;
    }
}

//.mt-1 => margin top
//spacing

@each $typekey,
$type in $spacing-types {

    //.m-1
    @each $sizekey,
    $size in $spacing-sizes {
        .#{$typekey}-#{$sizekey} {
            #{$type}: $size * $spacing-base-size;
        }
    }

    //.mx-1
    @each $sizekey,
    $size in $spacing-sizes {
        .#{$typekey}x-#{$sizekey} {
            #{$type}-left: $size * $spacing-base-size;
            #{$type}-right: $size * $spacing-base-size;
        }

        .#{$typekey}y-#{$sizekey} {
            #{$type}-top: $size * $spacing-base-size;
            #{$type}-bottom: $size * $spacing-base-size;
        }
    }

    //.mt-1
    @each $directionkey,
    $direction in $spacing-directions {

        @each $sizekey,
        $size in $spacing-sizes {
            .#{$typekey}#{$directionkey}-#{$sizekey} {
                #{$type}-#{$direction}: $size * $spacing-base-size;
            }
        }
    }

    .#{$typekey} {
        #{$type}: 0;
    }
}


/* 滑块样式 */
/* 滑块宽度 */
::-webkit-scrollbar {
    width: 1vmin;
}

/* 滑块两端 */
::-webkit-scrollbar-button {
    background-color: rgba(49, 132, 240, 0);
}

/* 滑块样式 */
::-webkit-scrollbar-thumb {
    background: linear-gradient(rgb(0, 162, 255), rgba(0, 162, 255, 0.306));
    border-radius: 2px;
    transition: 1s;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(rgb(38, 65, 221), rgb(143, 27, 158));
}

/* 滑块区背景 */
::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0);
}

::-webkit-scrollbar-corner {
    background-color: rgb(96, 4, 133);
}

// 模仿浙大样式区域

.fx-form .x-grid-col-12 {
    width: 100%;
}

.fx-field {
    line-height: 20px;
    padding: 7px 12px 12px;
    position: relative;
}

*,
:after,
:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.fx-form-separator .sep-line.style4 {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 38px;
    justify-content: center;
    position: relative;
}

.fx-form-separator .sep-line {
    height: 0;
}

.fx-form-separator .sep-line.style4 .sep-bg {
    border-radius: 16px 16px 0 0;
    bottom: 0;
    height: 28px;
    left: 0;
    opacity: .15;
    position: absolute;
    right: 0;
}

.fx-form-separator .sep-line.style4 .sep-center {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    max-width: 50%;
    min-width: 7%;
    overflow: hidden;
    padding: 0 38px;
    position: relative;
}

.fx-form-separator .sep-line.style4 .sep-center .sep-label {
    font-weight: 600;
    height: 100%;
    line-height: 38px;
    overflow: hidden;
    padding: 0 1px;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fx-form-separator .sep-line.style4 .sep-center .left-triangle {
    height: 10px;
    left: 0;
    position: absolute;
    top: 0;
    -webkit-transform: skew(-60deg) translateX(50%);
    transform: skew(-60deg) translateX(50%);
    width: 18px;
}

.fx-form-separator .sep-line.style4 .sep-center .left-border {
    border-radius: 0 0 0 16px;
    bottom: 0;
    left: 18px;
    position: absolute;
    top: 0;
    width: 21px;
}

.fx-form-separator .sep-line.style4 .sep-center .right-triangle {
    height: 10px;
    position: absolute;
    right: 0;
    top: 0;
    -webkit-transform: skew(60deg) translateX(-50%);
    transform: skew(60deg) translateX(-50%);
    width: 18px;
}

.fx-form-separator .sep-line.style4 .sep-center .right-border {
    border-radius: 0 0 16px;
    bottom: 0;
    position: absolute;
    right: 18px;
    top: 0;
    width: 21px;
}

.x-radio .x-radio-wrapper {
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.x-radio .radio-text {
    word-wrap: break-word;
    line-height: 22px;
    max-width: 100%;
    padding-left: 8px;
    vertical-align: middle;
    word-break: break-word;
}

.x-tag.multi-line .text-wrapper {
    margin: 5px;
}

.x-tag .text-wrapper {
    display: inline-block;
    max-width: 100%;

}

.fx-form-radiogroup .group-item.color-item .x-tag {
    vertical-align: middle;
}

.x-tag.is-rounded-rec {
    border-radius: 4px;
    border: 1px solid #d7d9dc;
    color: #141e31;
    border-color: #d7d9dc !important;

}


// 模仿浙大样式区域