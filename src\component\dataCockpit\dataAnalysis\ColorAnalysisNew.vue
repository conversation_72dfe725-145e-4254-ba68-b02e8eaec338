<template>
  <div class="color-analysis-container">
    <!-- 调试信息 -->
    <div v-if="showDebugInfo" class="debug-info">
      <h4>色号分析调试信息:</h4>
      <p>数据加载状态: {{ loading ? '加载中...' : '已完成' }}</p>
      <p>原始数据条数: {{ rawData.length }}</p>
      <p>色号TOP15数据长度: {{ colorTop15Data.colors?.length || 0 }}</p>
      <div class="debug-actions">
        <el-button size="small" type="primary" @click="retryLoadData">重新加载API</el-button>
        <el-button size="small" type="warning" @click="loadAllData">查询所有数据</el-button>
        <el-button size="small" type="success" @click="loadMockDataManually">加载模拟数据</el-button>
        <el-button size="small" type="info" @click="showDebugInfo = false">隐藏调试</el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载数据...">
      <div style="height: 200px;"></div>
    </div>

    <!-- 无数据提示 -->
    <div v-else-if="rawData.length === 0" class="no-data-container">
      <el-empty description="暂无数据，请检查API连接或上传Excel文件">
        <el-button type="primary" @click="retryLoadData">重新加载</el-button>
      </el-empty>
    </div>

    <!-- 正常内容 -->
    <template v-else>
      <!-- 色号分析组 -->
      <AnalysisChartGroup
        :raw-data="rawData"
        top15-title="TOP15色号回修率分析"
        top15-subtitle="按色号统计回修率和染色缸数"
        :top15-data="colorTop15Data"
        monthly-title="色号月度回修率分析"
        data-type="色号"
        data-field="colorCode"
        v-model:start-date="startDate"
        v-model:end-date="endDate"
        @apply-filter="applyFilter"
      />
    </template>
  </div>
</template>

<script setup>
import { getAnalysisData, transformApiDataToFrontend } from '@/api/dataAnalysis/dyeingAnalysis';
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import AnalysisChartGroup from './AnalysisChartGroup.vue';

// 状态管理
const rawData = ref([]);
const startDate = ref('');
const endDate = ref('');
const loading = ref(false);
const showDebugInfo = ref(true); // 临时调试开关

// 计算色号TOP15数据
const colorTop15Data = computed(() => {
  if (!rawData.value || rawData.value.length === 0) {
    return { colors: [], batches: [], repairRates: [] };
  }

  // 按色号分组统计
  const colorStats = {};
  rawData.value.forEach(item => {
    if (item.colorCode) {
      if (!colorStats[item.colorCode]) {
        colorStats[item.colorCode] = { total: 0, repairs: 0 };
      }
      colorStats[item.colorCode].total++;
      if (item.result === 0) {
        colorStats[item.colorCode].repairs++;
      }
    }
  });

  // 计算回修率并排序
  const colorArray = Object.entries(colorStats).map(([color, stats]) => ({
    color,
    batches: stats.total,
    repairRate: stats.total > 0 ? (stats.repairs / stats.total * 100) : 0
  }));

  // 按回修率降序排序，取前15个
  const top15 = colorArray
    .sort((a, b) => b.repairRate - a.repairRate)
    .slice(0, 15);

  return {
    colors: top15.map(item => item.color),
    batches: top15.map(item => item.batches),
    repairRates: top15.map(item => parseFloat(item.repairRate.toFixed(1)))
  };
});

// 从API加载数据
const loadDataFromAPI = async (params = {}) => {
  try {
    loading.value = true;
    const response = await getAnalysisData(params);
    
    // 适配新的API响应格式 (code: 0 表示成功)
    if (response.code === 0 && Array.isArray(response.data)) {
      console.log('色号分析 - API原始数据:', response.data);
      const transformedData = transformApiDataToFrontend(response.data);
      console.log('色号分析 - 转换后数据:', transformedData);
      rawData.value = transformedData;
      
      if (transformedData.length === 0) {
        ElMessage.warning('API返回数据为空，请检查查询条件');
      } else if (transformedData.length < 5) {
        ElMessage.warning(`API返回${transformedData.length}条数据，数据较少可能影响图表显示`);
      } else {
        ElMessage.success(`色号分析已加载${transformedData.length}条数据`);
      }
    } else {
      throw new Error(response.msg || '获取数据失败');
    }
  } catch (error) {
    console.error('色号分析 - 加载数据失败:', error);
    ElMessage.error(`加载数据失败: ${error.message}`);
    rawData.value = [];
  } finally {
    loading.value = false;
  }
};

// 应用筛选 - 触发数据重新计算
const applyFilter = () => {
  console.log('色号分析 - 应用筛选，日期范围:', { startDate: startDate.value, endDate: endDate.value });
  // 数据会通过计算属性自动更新
};

// 重新加载数据
const retryLoadData = async () => {
  console.log('色号分析 - 用户点击重新加载数据');
  const endDate = new Date();
  const startDate = new Date();
  startDate.setFullYear(startDate.getFullYear() - 1);
  
  await loadDataFromAPI({
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  });
};

// 查询所有数据 (不限制日期)
const loadAllData = async () => {
  console.log('色号分析 - 用户点击查询所有数据');
  await loadDataFromAPI({
    // 不传日期参数，获取所有数据
  });
};

// 手动加载模拟数据
const loadMockDataManually = () => {
  console.log('色号分析 - 用户手动加载模拟数据');
  
  const mockData = [
    {
      id: 1, colorCode: "G21A", dyeDate: "2025-01-15", 
      decathlonFabric: "BASIC LINING BR REC", deweiFabric: "T550180-84圈绒（环保纱版）",
      result: 1, year: 2025, month: 1
    },
    {
      id: 2, colorCode: "G21A", dyeDate: "2025-01-15",
      decathlonFabric: "BASIC LINING BR REC", deweiFabric: "T550180-84圈绒（环保纱版）", 
      result: 0, year: 2025, month: 1
    },
    {
      id: 3, colorCode: "K23A", dyeDate: "2025-01-14",
      decathlonFabric: "CATS MM REC", deweiFabric: "JPTQ0002 50/72低弹圈绒（GRS环保）-157-PPE",
      result: 1, year: 2025, month: 1
    },
    {
      id: 4, colorCode: "N06A", dyeDate: "2025-01-13",
      decathlonFabric: "JEREMY MM RPET MC", deweiFabric: "C018(环保纱）",
      result: 1, year: 2025, month: 1
    },
    {
      id: 5, colorCode: "N06A", dyeDate: "2025-01-13",
      decathlonFabric: "JEREMY MM RPET MC", deweiFabric: "C018(环保纱）",
      result: 0, year: 2025, month: 1
    },
    {
      id: 6, colorCode: "H33C", dyeDate: "2025-01-10",
      decathlonFabric: "OUTDOOR FABRIC PRO", deweiFabric: "T660220-防水透气面料",
      result: 1, year: 2025, month: 1
    },
    {
      id: 7, colorCode: "H33C", dyeDate: "2025-01-10",
      decathlonFabric: "OUTDOOR FABRIC PRO", deweiFabric: "T660220-防水透气面料",
      result: 0, year: 2025, month: 1
    },
    {
      id: 8, colorCode: "L88D", dyeDate: "2025-01-09",
      decathlonFabric: "COMFORT STRETCH", deweiFabric: "JPTQ0004 弹性纤维面料",
      result: 1, year: 2025, month: 1
    }
  ];
  
  rawData.value = mockData;
  console.log('✅ 色号分析 - 手动模拟数据已加载:', mockData.length, '条记录');
  ElMessage.success(`已手动加载${mockData.length}条模拟数据，色号分析界面应该正常显示了！`);
};

// 组件挂载时加载数据
onMounted(async () => {
  try {
    console.log('ColorAnalysisNew 组件开始挂载...');
    
    // 加载测试工具
    if (typeof window !== 'undefined') {
      import('@/utils/quickApiTest.js').catch(() => {
        console.log('测试工具加载失败，继续正常流程');
      });
    }
    
    // 检查API函数是否可用
    if (typeof getAnalysisData !== 'function') {
      console.warn('getAnalysisData 函数不可用，使用模拟数据');
      rawData.value = [];
      return;
    }
    
    // 默认加载最近1年的数据 (扩大范围以获取更多数据)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setFullYear(startDate.getFullYear() - 1);
    
    console.log('色号分析 - 准备加载API数据，日期范围:', {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    });
    
    await loadDataFromAPI({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    });
    
    console.log('色号分析 - API数据加载完成');
  } catch (error) {
    console.error('色号分析 - 组件挂载时加载数据失败:', error);
    console.log('错误类型:', typeof error);
    console.log('错误消息:', error.message);
    console.log('错误详情:', error);
    
    // 无论什么错误，都加载模拟数据以便测试界面
    console.log('🔧 色号分析 - API调用失败，加载模拟数据以便测试界面');
    
    // 加载模拟数据
    const mockData = [
      {
        id: 1, colorCode: "G21A", dyeDate: "2025-01-15", 
        decathlonFabric: "BASIC LINING BR REC", deweiFabric: "T550180-84圈绒（环保纱版）",
        result: 1, year: 2025, month: 1
      },
      {
        id: 2, colorCode: "G21A", dyeDate: "2025-01-15",
        decathlonFabric: "BASIC LINING BR REC", deweiFabric: "T550180-84圈绒（环保纱版）", 
        result: 0, year: 2025, month: 1
      },
      {
        id: 3, colorCode: "K23A", dyeDate: "2025-01-14",
        decathlonFabric: "CATS MM REC", deweiFabric: "JPTQ0002 50/72低弹圈绒（GRS环保）-157-PPE",
        result: 1, year: 2025, month: 1
      },
      {
        id: 4, colorCode: "N06A", dyeDate: "2025-01-13",
        decathlonFabric: "JEREMY MM RPET MC", deweiFabric: "C018(环保纱）",
        result: 1, year: 2025, month: 1
      },
      {
        id: 5, colorCode: "N06A", dyeDate: "2025-01-13",
        decathlonFabric: "JEREMY MM RPET MC", deweiFabric: "C018(环保纱）",
        result: 0, year: 2025, month: 1
      }
    ];
    
    rawData.value = mockData;
    console.log('✅ 色号分析 - 模拟数据已加载:', mockData.length, '条记录');
    ElMessage.warning('API连接失败，已加载模拟数据。请在浏览器中允许不安全内容后刷新页面以使用真实API。');
  }
});

// 处理Excel数据 - 接收来自父组件的数据
const processExcelData = (data) => {
  rawData.value = data;
  ElMessage.success(`色号分析已加载${data?.length || 0}条数据`);
};

// 暴露方法给父组件
defineExpose({
  processExcelData,
  loadDataFromAPI
});
</script>

<style scoped lang="scss">
.color-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;
  min-height: 500px;
}

.debug-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  
  h4 {
    margin: 0 0 10px 0;
    color: #856404;
  }
  
  p {
    margin: 5px 0;
    font-size: 14px;
    color: #856404;
  }
  
  .debug-actions {
    margin-top: 15px;
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 5px;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background: white;
  border-radius: 8px;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background: white;
  border-radius: 8px;
}
</style>
