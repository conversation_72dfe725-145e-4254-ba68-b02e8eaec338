<template>
    <div class="centerRight1">
        <br>
        <div>
            <div class="d-flex pt-2 pl-2">
                <span>
                    <slot name="icon">
                        <i class="iconfont icon-align-left" />
                    </slot>
                </span>
                <span class="fs-xxx text mx-2">
                    <slot name="title">

                    </slot>
                </span>
            </div>
            <div class="d-flex ai-center flex-column body-box">
                <dv-capsule-chart class="dv-cap-chart" :config="config" />
                <slot name="chart"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue'
const config = reactive({
    data: [
        {
            name: '数据1',
            value: 167
        },
        {
            name: '数据2',
            value: 67
        },
        {
            name: '数据3',
            value: 123
        },
        {
            name: '数据4',
            value: 55
        },
        {
            name: '数据5',
            value: 98
        }
    ]
})
</script>

<style lang="scss" scoped>
.centerRight1 {
    height: inherit;

    width: inherit;

    border-radius: 5px;

    .bg-color-black {

        height: inherit;

        width: inherit;
        border-radius: 10px;
    }

    .text {
        color: #c3cbde;
    }

    .body-box {
        border-radius: 10px;
        overflow: hidden;

        .dv-cap-chart {
            width: 100%;
            height: 160px;
        }
    }
}
</style>
