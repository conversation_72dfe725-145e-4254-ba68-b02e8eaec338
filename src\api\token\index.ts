import { refresh } from "../auth";
/**
 * 
 * @returns 
 * 获取 token 的函数
 */
function getToken() {
    return localStorage.getItem("token"); // 假设 token 存储在 localStorage 中
}

/**
 * 
 * @returns 
 * token有效性校验
 */
function isTokenValid(): boolean {
    try {
        let tokenTime: any = localStorage.getItem("tokenTime");
        let now = Date.now();
        // 将 tokenTime 转换为 Date 对象
        let tokenDate = new Date(tokenTime.replace(/-/g, '/')); // 替换为 '/' 以兼容不同浏览器
        // 计算50分钟的毫秒数 目前token过期时间是24H 
        const fiftyMinutesInMs = 23 * 60 * 60 * 1000;
        // 比较时间
        if (now - tokenDate.getTime() > fiftyMinutesInMs) {
            return false
        } else {
            let token = getToken()
            refresh(token)
            return true
        }
    }
    catch {
        return false
    }
}
export { getToken, isTokenValid, refresh };
