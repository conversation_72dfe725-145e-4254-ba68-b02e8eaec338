import axios from "axios";
import { ElMessage } from "element-plus";
import { getToken } from "@/api/token";
import { ElLoading } from "element-plus";
import { ref } from "vue";

const loadingInstance = ref(null);

const token = getToken;
const service = axios.create({
    baseURL: "https://texwell.com.cn:8321",
    // timeout: 5000,
});

service.interceptors.request.use(
    (config) => {
        // 显示加载状态
        loadingInstance.value = ElLoading.service({
            lock: true, // 锁定屏幕，防止操作
            text: '加载中，请稍候...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.6)', // 深色半透明背景
            customClass: 'custom-loading', // 自定义样式类
        });


        // 处理Authorization头
        config.url.includes("RefreshToken")
            ? delete config.headers["Authorization"]
            : (config.headers["Authorization"] = `${token()}`);

        return config;
    },
    (error) => {
        // 关闭加载状态
        if (loadingInstance.value) {
            loadingInstance.value.close();
        }
        ElMessage.error(`错误:${error}`);
        return Promise.reject(error);
    }
);

service.interceptors.response.use(
    (response) => {
        // 关闭加载状态
        if (loadingInstance.value) {
            loadingInstance.value.close();
        }
        return response;
    },
    (error) => {
        // 关闭加载状态
        if (loadingInstance.value) {
            loadingInstance.value.close();
        }
        ElMessage.error(`错误:${error}`);
        return Promise.reject(error);
    }
);

export default service;
