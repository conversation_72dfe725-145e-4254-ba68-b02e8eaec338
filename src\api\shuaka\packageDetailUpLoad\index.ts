import service from '@/request/index'
/**
 * 详细包装信息
 */
interface packageDetailMore {
    DocType: string;
    gkh: string;
    ph: number;
    QuFuKuan: number;
    FuKuan: number;
    Ke<PERSON>hong: number;
    MaoZhong: number;
    Jing<PERSON>hong: number;
    ShiJiMiChang: number;
    <PERSON><PERSON><PERSON>: number;
    <PERSON><PERSON>hong: number;
    <PERSON><PERSON><PERSON>: number;
    <PERSON><PERSON>hong: number;
    GongJinMiChang: number;
    LuRuDate: string;
    BaoZhuangFangShi: string;
    IsInWhsCode: string;
    bmdz: string;
    UserCode: string;
    Classes: string;
    ZhiGuanZhongLiang: number;
    BiaoQianQty: number;
    BatchGkh: string;
    CreateOn: string;
    CreateBy: string;
    IsRepeat: number;
    IsComplete: number;
    WhsCode: string;
    WhsName: string;
    CardCode: string;
    CardName: string;
    bcpinfo: string;
}
/**
 * 精简的包装明细
 */
interface packageDetailLess {
    gkh: string;
    pc: number;
}
/**
 * 获取工卡基本信息
 * @param  gkh - 工卡列表
 * @returns 
 */
const GetGkData = async (gkh: Array<String>, type: string) => {
    return await service({
        url: `/api/Product/GetGkData`,
        method: "post",
        data: {
            gkh: gkh,
            type: type
        },
        timeout: 5000
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
/**
 * 保存详细包装明细
 * @param packageDetail - 包装明细
 * @returns 
 */
const PackageDetailSubmit = async (packageDetail: Array<packageDetailMore>) => {
    return await service({
        url: `/api/Product/PackageDetailSubmit`,
        method: "post",
        data: packageDetail,
        timeout: 5000
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
/**
 * 获取工卡加工供应商
 * @param packageDetail - 包装明细
 * @returns 
 */
const WwBzCheck = async (packageDetail: Array<packageDetailLess>) => {
    return await service({
        url: `/api/Product/WwBzCheck`,
        method: "post",
        data: packageDetail,
        timeout: 5000
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
export { GetGkData, WwBzCheck, PackageDetailSubmit, packageDetailMore, packageDetailLess }