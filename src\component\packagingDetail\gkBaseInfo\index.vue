<template>
    <div>
        <div class="container">
            <el-form-item label="成品/半成品:" style="width: 200px">
                <el-select v-model="bcpinfo">
                    <el-option label="成品" value="成品"></el-option>
                    <el-option label="半成品" value="半成品"></el-option>
                </el-select>
            </el-form-item>
            <el-button type="warning" @click="fillIn">补全完整明细</el-button>
            <excelModel></excelModel>
        </div>
        <el-table v-if="detailMinOrMore" :data="packDetail" style="width: 100%">
            <el-table-column prop="工卡号" label="工卡号" width="180"></el-table-column>
            <el-table-column prop="匹号" label="匹号" width="180"></el-table-column>
            <el-table-column prop="公斤米长" label="公斤米长" width="180"></el-table-column>
            <el-table-column prop="重量" label="重量" width="180"></el-table-column>
            <el-table-column prop="等级" label="等级" width="180"></el-table-column>
        </el-table>
        <el-table v-if="!detailMinOrMore" :data="packageDetailMoreData" style="width: 100%;height: 400px;">
            <el-table-column prop="DocType" label="包装类型" />
            <el-table-column prop="bcpinfo" label="成品/半成品" />
            <el-table-column prop="gkh" label="工卡号" />
            <el-table-column prop="ph" label="匹号" />
            <el-table-column prop="QuFuKuan" label="全幅宽" />
            <el-table-column prop="FuKuan" label="幅宽" />
            <el-table-column prop="KeZhong" label="克重" />
            <el-table-column prop="MaoZhong" label="毛重" />
            <el-table-column prop="JingZhong" label="净重" />
            <el-table-column prop="ShiJiMiChang" label="实际米长" />
            <el-table-column prop="MaChang" label="码长" />
            <el-table-column prop="MiZhong" label="米重" />
            <el-table-column prop="MaZhong" label="码重" />
            <el-table-column prop="BangZhong" label="磅重" />
            <el-table-column prop="GongJinMiChang" label="公斤米长" />
            <el-table-column prop="LuRuDate" label="录入日期" />
            <el-table-column prop="BaoZhuangFangShi" label="包装方式" />
            <el-table-column prop="IsInWhsCode" label="是否在仓库" />
            <el-table-column prop="bmdz" label="等级" />
            <el-table-column prop="IsComplete" label="是否完成" />
        </el-table>
    </div>
</template>

<script setup lang="ts">
import excelModel from '@/component/packagingDetail/excelModel/index.vue'
import { GetGkData, WwBzCheck, packageDetailLess, packageDetailMore } from '@/api/shuaka/packageDetailUpLoad';
import { useStore } from 'vuex';
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatTime } from '@/utils/index'
const bcpinfo = ref<'成品' | '半成品'>(null)
const store = useStore();
/**
 * 精简包装明细
 */
const packDetail = computed(() => {
    return store.state.packDetail
});
/**
 * 详细包装明细
 */
const packageDetailMoreData = computed(() => {
    return store.state.packageDetailMore
});
/**
 * 详细包装明细是否补全
 */
const detailMinOrMore = computed(() => {
    return store.state.packageDetailMore.length == 0
})
/**
 * 获取工卡信息和供应商信息
 * @param gkhList 工卡列表
 * @param packDetail 精简包装明细
 * @returns 工卡基础信息和供应商信息
 */
const gkBaseInfo = async (packDetail: Array<packageDetailLess>) => {
    const gkhListpost = new Set()
    const packDetailPost = []
    for (let index = 0; index < packDetail.length; index++) {
        const element = packDetail[index];
        packDetailPost.push({
            gkh: element["工卡号"],
            ph: element["匹号"]
        })
        gkhListpost.add(element["工卡号"])
    }
    const gkhListpostArray: Array<any> = Array.from(gkhListpost);
    const [baseInfo, supplierInfo] = await Promise.all([
        GetGkData(gkhListpostArray, "染整"),
        WwBzCheck(packDetailPost)
    ]);
    return [baseInfo, supplierInfo]
}
/**
 * 工卡基础信息和工卡供应商信息合并
 * @param  info 工卡基础信息和供应商信息
 */
const completeInfo = (info: Array<any>) => {
    const baseInfo: Array<Object> = info[0].data.response
    const supplierInfo: Array<Object> = info[1].data.response
    const merged = {};

    // 处理第一个数据源
    baseInfo.forEach(item => {
        if (item["info"]) {
            merged[item["gkh"]] = { ...item["info"] };
        }
    });
    // 处理第二个数据源
    supplierInfo.forEach(item => {
        if (item["info"]) {
            if (merged[item["gkh"]]) {
                // 如果gkh已存在，合并info
                merged[item["gkh"]] = { ...merged[item["gkh"]], ...item["info"] };
            } else {
                // 如果gkh不存在，直接添加
                merged[item["gkh"]] = { ...item["info"] };
            }
        }
    });
    return merged;

}
/**
 * 生成最终的完整工卡包装明细
 * @param completeInfo - 工卡完整基础信息
 */
const createPackageDetailMore = (completeInfo) => {
    const val = []
    if (bcpinfo.value == null) {
        throw Error("成品/半成品信息未输入!")
    }
    for (let index = 0; index < packDetail.value.length; index++) {
        const element = JSON.parse(JSON.stringify(packDetail.value[index]));
        const gkh = element["工卡号"]
        const weight = Number(element["重量"]); // 将重量字符串转换为浮点数
        const gongJinMiChang = element["公斤米长"];
        // 计算
        const MaoZhong = weight;
        const JingZhong = weight;
        const ShiJiMiChang = (weight * gongJinMiChang).toFixed(1); // 保留0位小数
        const MaChang = ((weight * gongJinMiChang) / 0.9144).toFixed(1); // 保留0位小数
        const MiZhong = (1 / gongJinMiChang * 1000).toFixed(1); // 保留1位小数
        const MaZhong = (1 / gongJinMiChang * 1000 * 0.9144).toFixed(1); // 保留1位小数
        const BangZhong = (weight * 2.204).toFixed(1); // 保留1位小数

        const data = {
            "DocType": "委外",
            "gkh": element["工卡号"],
            "ph": element["匹号"],
            "QuFuKuan": completeInfo[gkh]["BiaoZhunFuKuan"],
            "FuKuan": completeInfo[gkh]["BiaoZhunFuKuan"],
            "KeZhong": completeInfo[gkh]["BiaoZhunKeZhong"],
            "MaoZhong": MaoZhong,
            "JingZhong": JingZhong,
            "ShiJiMiChang": ShiJiMiChang,
            "MaChang": MaChang,
            "MiZhong": MiZhong,
            "MaZhong": MaZhong,
            "BangZhong": BangZhong,
            "GongJinMiChang": element["公斤米长"],
            "LuRuDate": formatTime(Date.now(), 'yyyy-MM-dd HH:mm:ss'),//
            "BaoZhuangFangShi": completeInfo[gkh]["BaoZhuangYaoQiu"].replace("按", ''),
            "IsInWhsCode": "N",
            "bmdz": element["等级"],
            "UserCode": "306065",//
            "Classes": "A",//
            "ZhiGuanZhongLiang": 0,
            "BiaoQianQty": 1,
            "BatchGkh": element["匹号"],
            "CreateOn": formatTime(Date.now(), 'yyyy-MM-dd HH:mm:ss'),//
            "CreateBy": "王显云",
            "IsRepeat": 1,
            "IsComplete": 1,
            "WhsCode": "301",
            "WhsName": "A仓-染整成品",
            "CardCode": completeInfo[gkh]["CardCode"],
            "CardName": completeInfo[gkh]["CardName"],
            "bcpinfo": bcpinfo.value,
            "BzStatus": "是",
            "sfbcp": bcpinfo.value
        };
        val.push(data)
    }
    return val
}
/**
 * 
 */
const SpCheckCxGkhError = (val) => {
    const baseInfo: Array<Object> = val[0].data.response
    const errorGkh = []
    for (let index = 0; index < baseInfo.length; index++) {
        const element = baseInfo[index];
        if (element["info"] == null) {
            errorGkh.push(element["gkh"])
        }
    }
    if (errorGkh.length > 0) {
        throw Error(`以下工卡不存在或有重修工卡:${JSON.stringify(errorGkh)}`)
    }
}
/**
 * 补全表格里的完整包装明细
 */
const fillIn = async () => {
    if (packDetail.value.length == 0) {
        ElMessageBox.confirm(String("还未上传excel！"))
        return
    }
    await gkBaseInfo(packDetail.value).then(val => {
        SpCheckCxGkhError(val)
        const data = completeInfo(val)
        store.commit("setpackageDetailMore", createPackageDetailMore(data))
    }).catch((error) => {
        ElMessageBox.confirm(String(error))
    })
}

/**
 * 清除成品/半成品选择
 */
const clearBcpInfo = () => {
    bcpinfo.value = null
}

/**
 * 暴露方法和状态给父组件调用
 */
defineExpose({
    fillIn,
    bcpinfo,
    clearBcpInfo
})
</script>

<style scoped lang="scss">
/* 可以在这里添加样式 */
.container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: aliceblue;
    padding: 20px;
    /* 添加容器内边距 */
    gap: 16px;
    /* 元素之间的间距 */

    /* 调整 el-form-item 的对齐 */
    :deep(.el-form-item) {
        margin-bottom: 0;
        /* 移除默认的底部边距 */
        display: flex;
        align-items: center;
        /* 确保标签和控件垂直居中 */
    }

    :deep(.el-form-item__content) {
        display: flex;
        align-items: center;
        /* 确保内容垂直居中 */
    }
}
</style>