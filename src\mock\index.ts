import Mock from 'mockjs'
const mock = Mock
mock.setup({ timeout: 500 })

const productionData = () => {

    return {
        status: 200,
        success: true,
        msg: "获取成功",
        response: {
            RzDayOrder: 100,
            RzMonthOrder: 3000,
            RzIHoding: 200,
            RzIIHoding: 150,
            RzHoding: 350,
            RzDayIProcution: 120,
            RzDayIIProcution: 80,
            RzDayProcution: 200,
            RzMonthIProcution: 2400,
            RzMonthIIProcution: 1600,
            RzMonthProcution: 4000,
            RzDayShipment: 500,
            RzMonthShipment: 600,
            ThDayOrder: 700,
            ThMonthOrder: 800,
            ThIHoding: 900,
            ThIIHoding: 1000,
            ThHoding: 1100,
            ThDayIProcution: 1200,
            ThDayIIProcution: 1300,
            ThDayProcution: 1400,
            ThMonthIProcution: 1500,
            ThMonthIIProcution: 1600,
            ThMonthProcution: 4000,
            ThDayShipment: 500,
            ThMonthShipment: 600,

        }
    }
}
// mock.mock(/GetSGAllData.*/, 'get', productionData)
export default mock