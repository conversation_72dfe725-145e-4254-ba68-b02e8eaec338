<template>
    <div class="container">
        <div class="input-group">
            <el-input v-model="store.state.phone" placeholder="请输入手机号" maxlength="11" type="tel"></el-input>
            <el-button :disabled="isCounting" @click="getVerificationCode">
                {{ isCounting ? `${countdown}秒后重试` : '获取验证码' }}
            </el-button>
        </div>
        <div class="input-group">
            <el-input v-model="store.state.verificationCode" placeholder="请输入验证码" maxlength="6"></el-input>
            <el-button @click="buttonClick" type="danger">确定</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getYzm } from '@/api/createVisit';
import { formatTime, openVn } from '@/utils/index';
import store from '@/store';
import { ElMessageBox } from 'element-plus';
const emit = defineEmits(['inFocus', 'submit']);

function buttonClick() {
    confirmCode();
    emit('submit');

}


let yzmTime: any = localStorage.getItem("yzmTime")
let minCha: number
// 验证码时间差计算
if (yzmTime == null || yzmTime == "null" || yzmTime == undefined) {
    minCha = 100
} else {
    minCha = Math.floor((Date.now() -
        new Date(yzmTime.replace(/-/g, '/')).getTime()) / (1000 * 60))
}
if (minCha < 30) {
    store.state.verificationCode = localStorage.getItem('yzm')
}
const isCounting = ref(false);
const countdown = ref(60);
/**
 * 发送短信验证码
 */
const getVerificationCode = () => {
    if (!/^\d{11}$/.test(store.state.phone)) {
        ElMessageBox.confirm('请输入有效的手机号!', '友情提醒:', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {

        }).catch(() => {
            // 用户点击取消时的处理逻辑
        });
        return;
    }
    getYzm(store.state.phone).then((val) => {
        console.log(val);
        try {
            openVn(val.data.msg)
            if (val.data.success === false) {

            } else {
                startCountdown();
            }
        } catch (error) {
            openVn(val.response.data.msg)
        }
        // 处理成功逻辑
    }).catch((error) => {
        // 处理失败逻辑
        openVn("验证码发送失败")
    });

};
/**
 * 验证码倒计时
 */
const startCountdown = () => {
    isCounting.value = true;
    countdown.value = 60;

    const interval = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
            clearInterval(interval);
            isCounting.value = false;
        }
    }, 1000);
};

const confirmCode = () => {
    localStorage.setItem('yzm', store.state.verificationCode);
    localStorage.setItem('yzmTime', formatTime(Date.now(), "yyyy-MM-dd HH:mm:ss"));

};
</script>

<style scoped>
.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

}

.input-group {
    margin-top: 30px;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 300px;
    /* 可根据需要调整 */
    margin-bottom: 10px;
}

.input-group el-input {
    flex: 1;
    width: 50px;
    /* 输入框占据50%宽度 */
    margin-right: 10px;
}

.input-group el-button {
    min-width: 100px;
}

/* 新增样式以确保输入框和按钮在同一行 */
.input-group {
    justify-content: space-between;
    /* 确保输入框和按钮之间的间距 */
}
</style>
