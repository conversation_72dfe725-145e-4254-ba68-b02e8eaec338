.app-container {
    height: 100vh;
    background: #f0f2f5;

    .header {
        background: #2d3a4b;
        padding: 20px 40px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative; // 新增定位属性


        .title {
            color: #fff;
            margin: 0;
            font-size: 24px;
            letter-spacing: 2px;
        }

        // 新增header内容区块
        .header-content {
            display: flex;
            align-items: center;
        }

        // 新增logo样式
        .header-logo {
            height: 1em;
            margin-right: 10px;
            transform: scale(1.8);
        }

        .user-menu {
            display: flex;
            align-items: center;
            position: absolute; // 保留原有定位
            top: 10px;
            right: 10px;

            .user-trigger {
                color: #e9eef3;
                cursor: pointer;
                padding: 0 10px;
                transition: all 0.3s;

                &:hover {
                    background: #f5f7fa;

                    .user-icon {
                        color: #409eff;
                    }
                }
            }
        }
    }

    .container {
        display: flex;
        height: calc(100vh - 80px);
    }

    // 下拉菜单样式优化
    .el-dropdown-menu__item {
        display: flex;
        align-items: center;
        padding: 10px 15px !important;

        .el-icon {
            margin-right: 8px;
            font-size: 16px;
        }
    }
}

.sidebar {
    width: 240px;
    background: #fff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    .sidebar-header {
        padding: 15px;
        background: #409EFF;

        .sidebar-title {
            color: #fff;
            margin: 0;
            font-size: 16px;
        }
    }
}

.custom-menu {
    border-right: none;

    .menu-item {
        margin: 6px 10px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
            background: #ecf5ff;
            transform: translateX(3px);
        }

        &.is-active {
            background: #409EFF;
            color: #fff !important;
        }
    }
}

.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.operation-header {
    margin-bottom: 20px;

    .header-container {
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }
}

.form-section {
    display: grid;
    gap: 20px;
}

.form-card {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .form-title {
        color: #303133;
        font-size: 18px;
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 2px solid #409EFF;

        .title-icon {
            color: #409EFF;
            margin-right: 8px;
        }
    }
}

.form-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.form-item {
    padding: 15px;
    background: #fff;
    transition: all 0.3s;

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }


}
.input-label {
    color: #606266;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
    width: 280px;
}
.narrow-input {
    width: 100%;

    &::v-deep(.el-input__inner) {
        border-radius: 4px;
    }
}

.submit-btn {
    width: auto;
    min-width: 120px;
    margin-top: 20px;
    padding: 12px 24px;
    font-size: 14px;
    letter-spacing: 1px;
    transition: all 0.3s;

    i {
        margin-right: 8px;
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
}

.work-card-input {
    &::v-deep(.el-input__prefix) {
        display: flex;
        align-items: center;
        padding-left: 8px;
    }
}

.process-select {
    width: 400px;

    &::v-deep(.el-input__inner) {
        border-radius: 20px;
    }
}

.card {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
}

.details-card {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .form-title {
        color: #303133;
        font-size: 16px;
        margin: 0 0 15px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.detail-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;

    .label {
        color: #606266;
        font-size: 14px;
        min-width: 100px;
        font-weight: 500;
    }

    .value {
        color: #303133;
        font-size: 14px;
        flex: 1;
    }
}

// 添加高亮样式
.highlight {
    color: #ff0000;
    font-weight: bold;
    &::after {
        content: '（当前）';
        font-size: 0.8em;
    }
}



.storage-status {
    margin-left: 10px;
    font-size: 12px;
    color: #666;
    
    &.has-data {
        color: #67C23A;
    }
}




.el-menu-item {
    a {
        display: block;
        width: 100%;
        height: 100%;
        padding: 0 20px;
        color: inherit !important; 
    }

    // 保持激活状态样式
    &.is-active {
        background-color: #ecf5ff;
        border-bottom: 2px solid #409eff;

        a {
            color: #409eff !important; // 激活状态文字颜色
        }
    }
}


/* 包装明细布局样式 */
.packaging-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 20px;
    max-width: 1440px;
    margin: 0 auto;

    /* 滚动容器样式 */
    .scroll-container {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 20px; // 防止底部遮挡

        /* 主表间距 */
        .main-table {
            margin-bottom: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        /* 明细表样式 */
        .detail-table {
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }
    }

    /* 头部样式 */
    .type-header {
        margin-bottom: 24px;
        text-align: center;

        h2 {
            color: #409eff;
            font-size: 24px;
            font-weight: 600;
        }
    }
}


