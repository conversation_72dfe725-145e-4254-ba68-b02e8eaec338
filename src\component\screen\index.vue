<template>
    <div>
        <el-button type="primary" @click="startScanning" v-if="!isScanning">扫描</el-button>
        <el-button type="danger" @click="stopScanning" v-if="isScanning">停止扫描</el-button>
        <div v-show="show" id="reader" style="width: 400px; height: 400px; margin-top: 1px;"></div>
    </div>
</template>

<script setup>
import { ref, onBeforeUnmount } from 'vue';
import { Html5Qrcode } from 'html5-qrcode';
import { ElButton } from 'element-plus';
import { writeOff } from '@/api/writeOff';
import { openVn } from '@/utils';

const qrCodeResult = ref(null);
const isScanning = ref(false);
let html5QrCode;
let show = ref(false)
const startScanning = () => {
    show.value = true
    html5QrCode = new Html5Qrcode("reader");
    const qrCodeSuccessCallback = (decodedText) => {
        qrCodeResult.value = decodedText;
        writeOff(decodedText, 0).then(() => {
            openVn("已扫描")
        }).catch(() => {
            openVn("二维码无效")
        })
        stopScanning(); // 成功扫描后停止
    };

    const qrCodeErrorCallback = (errorMessage) => {
        console.warn("扫描错误: ", errorMessage);
    };

    html5QrCode.start(
        { facingMode: { exact: "environment" } },
        {
            fps: 30,
            qrbox: 300
        },
        qrCodeSuccessCallback,
        qrCodeErrorCallback
    ).then(() => {
        isScanning.value = true; // 更新状态为正在扫描
    }).catch(err => {
        console.error("启动摄像头时出错: ", err);
    });
};

const stopScanning = () => {
    show.value = false
    if (html5QrCode) {
        html5QrCode.stop().then(() => {
            isScanning.value = false; // 更新状态为停止扫描
        }).catch(err => {
            console.error("停止扫描时出错: ", err);
        });
    }
};

// 组件销毁时停止扫描
onBeforeUnmount(() => {
    stopScanning();
});
</script>

<style scoped>
#reader {
    border: 1px solid #ccc;
}
</style>