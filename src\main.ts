import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import dataV from "@jiaminghi/data-view";
// 引入全局css
import "./assets/scss/style.scss";
// 引入图表（所有图标见 icon 目录下的 demo_index.html）
import "./assets/icon/iconfont.css";
// // 引入 全局注册组件
// import PublicComponent from "@/components/componentInstall";
import "./mock/index";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from 'element-plus/es/locale/lang/zh-cn'; // 引入中
// import "element3/lib/theme-chalk/index.css";
// import Element3 from "element3";

const app = createApp(App);
// app.use(PublicComponent);
app.use(dataV);
app.use(store);
app.use(router);
// app.use(Element3);
app.use(ElementPlus, { locale: zhCn }); // 设置语言为中文
app.mount("#app");
