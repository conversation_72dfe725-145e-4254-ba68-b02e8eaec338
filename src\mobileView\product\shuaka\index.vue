<template>
    <div class="app-container">
        <div class="container">
            <div class="sidebar">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">工序选择</h3>
                </div>
                <el-menu :default-active="selectedProcess" @select="selectProcess" unique-opened class="custom-menu"
                    style="height: calc(100vh - 120px); overflow-y: auto;">
                    <el-menu-item v-for="(item, key) in filteredProcesses" :key="key" :index="item.key"
                        class="menu-item">
                        {{ item.name }}
                    </el-menu-item>
                </el-menu>
            </div>
            <div class="main-content">
                <div class="operation-header">
                    <div class="header-container card">
                        <label class="input-label">请选择工序</label>
                        <el-select v-model="selectedProcess" placeholder="选择工序" filterable remote :loading="loading"
                            class="process-select">
                            <el-option v-for="(item, key) in filteredProcesses" :key="key" :label="item.name"
                                :value="item.key"></el-option>
                        </el-select>
                        <el-input v-model="workCardNumber" placeholder="请输入工卡号" maxlength="15" class="work-card-input"
                            clearable @keyup.enter="getMaterialAndGkInfo" ref="workCardInput">
                            <template #prefix>
                                <i class="el-icon-user"></i>
                            </template>
                        </el-input>
                        <!-- 其他部分保持不变 -->
                        <el-select v-model="workerinfo" placeholder="选择员工号" filterable clearable
                            :filter-method="filterWorker" @blur="handleWorkerBlur">
                            <el-option v-for="item in filteredWorkerList" :key="item.sWorkerNo"
                                :label="item.sWorkerName" :value="item.sWorkerNo" />
                        </el-select>
                        <el-select v-model="equipmentinfo" placeholder="请选择机台" filterable clearable
                            :filter-method="filterEquipment" @blur="handleEquipmentBlur">
                            <el-option v-for="item in filteredEquipmentList" :key="item.sEquipmentNo"
                                :label="item.sEquipmentName" :value="item.sEquipmentNo" />
                        </el-select>
                    </div>
                </div>
                <!-- 新增的详细信息表格 -->
                <div class="details-card card">
                    <h3 class="form-title">详细信息|已扫工卡号:{{ workCardNumberCopy }}<h3 v-if="bCompleted"
                            style="color: red;font-weight: 900;">已完成</h3>
                    </h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="label">物料编号</span>
                            <span class="value">{{ materialInfo.itemCode }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">色号</span>
                            <span class="value">{{ materialInfo.sh }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">物料名称</span>
                            <span class="value">{{ materialInfo.itemName }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">色名</span>
                            <span class="value">{{ materialInfo.khsm }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">产量</span>
                            <el-input type="number" class="value" v-model="materialInfo.mic"></el-input>
                        </div>
                        <div class="detail-item">
                            <span class="label">开卡匹数</span>
                            <span class="value">{{ materialInfo.ps }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">工艺路线</span>
                            <span class="value">
                                <span v-for="(gx, index) in materialInfo.gylx" :key="index">
                                    <span :class="{ 'highlight': index === materialInfo.nextGxNum - 1 }">
                                        {{ gx.gxmc }}
                                    </span>
                                    <span v-if="index < materialInfo.gylx.length - 1"> > </span>
                                </span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="label">下一道工序</span>
                            <span class="value">{{ materialInfo.nextGx }}</span>
                        </div>
                    </div>
                </div>
                <div class="form-section">
                    <div class="form-card" v-if="selectedProcess">
                        <h3 class="form-title">
                            <span class="title-icon">▶</span>
                            {{ content[selectedProcess]?.name }} {{ currentEndInputs ? '开始' : '' }}
                        </h3>
                        <div class="form-container">
                            <div class="form-item card" v-for="(input, index) in currentBeginInputs" :key="index">
                                <div class="input-wrapper">
                                    <label class="input-label">{{ input.label }}</label>
                                    <el-input v-if="input.valueType === 'text'" v-model="input.value"
                                        :placeholder="input.label" class="narrow-input" clearable>
                                    </el-input>
                                    <el-input-number v-if="input.valueType === 'number'" v-model="input.value"
                                        :placeholder="input.label" class="narrow-input" controls-position="right">
                                    </el-input-number>
                                    <el-select v-if="input.valueType === 'select'" v-model="input.value"
                                        placeholder="请选择" class="narrow-input" clearable filterable>
                                        <el-option v-for="(option, idx) in input.valueList" :key="idx" :label="option"
                                            :value="option"></el-option>
                                    </el-select>

                                    <!-- 新增时间选择器 -->
                                    <el-time-picker
                                        v-if="input.valueType === 'time'"
                                        v-model="input.value"
                                        placeholder="选择时间"
                                        format="HH:mm:ss"
                                        value-format="HH:mm:ss"
                                        class="narrow-input"
                                    ></el-time-picker>

                                </div>
                            </div>
                        </div>
                        <el-button type="primary" class="submit-btn"
                            @click="saveInputs(currentBeginInputs, !currentEndInputs)">
                            <i class="el-icon-upload"></i>
                            保存开始数据
                        </el-button>
                    </div>

                    <div class="form-card" v-if="currentEndInputs && selectedProcess">
                        <h3 class="form-title">
                            <span class="title-icon">■</span>
                            {{ content[selectedProcess]?.name }} 结束
                        </h3>
                        <div class="form-container">
                            <div class="form-item card" v-for="(input, index) in currentEndInputs" :key="index">
                                <div class="input-wrapper">
                                    <label class="input-label">{{ input.label }}</label>
                                    <el-input v-if="input.valueType === 'text'" v-model="input.value"
                                        :placeholder="input.label" class="narrow-input" clearable>
                                    </el-input>
                                    <el-input-number v-if="input.valueType === 'number'" v-model="input.value"
                                        :placeholder="input.label" class="narrow-input" controls-position="right">
                                    </el-input-number>
                                    <el-select v-if="input.valueType === 'select'" v-model="input.value"
                                        placeholder="请选择" class="narrow-input" clearable filterable>
                                        <el-option v-for="(option, idx) in input.valueList" :key="idx" :label="option"
                                            :value="option"></el-option>
                                    </el-select>
                                    <!-- 新增时间选择器 -->
                                    <el-time-picker
                                        v-if="input.valueType === 'time'"
                                        v-model="input.value"
                                        placeholder="选择时间"
                                        format="HH:mm:ss"
                                        value-format="HH:mm:ss"
                                        class="narrow-input"
                                    ></el-time-picker>

                                </div>
                            </div>
                        </div>
                        <el-button type="success" class="submit-btn" @click="saveInputs(currentEndInputs, true)">
                            <i class="el-icon-check"></i>
                            保存结束数据
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
//加载引用
import { GetWorkerAndEquipmentByProcedure, gkInfo, gkTrack } from '@/api/shuaka';
import { formatTime } from '@/utils';
import { ElMessageBox } from 'element-plus';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { content } from './data';
//定义数据
// 定义工卡输入框的引用
const workCardInput = ref(null);
const bCompleted = ref(false)
/**
 * 报工数据
 */
const gkTrackInfo = ref<any>({
    "sCardNo": "",
    "GXNo": "",
    "tFactStartTime": "",
    "tFactEndTime": "",
    "sLocation": "",
    "sEquipmentNo": "",
    "sWorkerGroupNo": "",
    "sWorkerGroupName": "",
    "sWorkerNoList": "",
    "sWorkerNameList": "",
    "sUnionCardNoList": "",
    "nTrackQty": 0,
    "sUserID": "",
    "sRemark": "",
    "nTrackQtyEx": 0,
    "extra": {}
})
/**
 * 工人清单数据
 */
const workerList = ref<Array<any>>([]);
/**
 * 设备清单数据
 */
const equipmentList = ref<Array<any>>([]);

/**
 * 员工号输入框的关键词
 */
const workerQuery = ref('');
/**
 * 设备输入框的关键词
 */
const equipmentQuery = ref('');
/**
 * 工序数据
 */
const selectedProcess = ref('');
/**
 * 工卡号
 */
const workCardNumber = ref('');
/**
 * 工卡号备份
 */
const workCardNumberCopy = ref('');
/**
 * 开始报工环节的输入框集合
 */
const currentBeginInputs = ref([]);
/**
 * 结束报工环节的输入框集合
 */
const currentEndInputs = ref([]);
/**
 * 加载画面显示状态
 */
const loading = ref(false);
/**
 * 工序和各报工环节输入框集合
 */
const filteredProcesses = ref(Object.entries(content).map(([key, item]) => ({ key, ...item })));
/**
 * 工卡和物料信息
 */
const materialInfo = ref({
    itemCode: '',
    itemName: '',
    sh: '',
    khsm: '',
    mic: '',
    ps: '',
    gylx: [] as Array<{ gxbm: string; gxmc: string }>, // 修改为对象数组
    nextGx: '',
    nextGxCode: '',
    nextGxNum: 0
});
/**
 * 机台
 */
const equipmentinfo = ref(null)
/**
 * 员工
 */
const workerinfo = ref(null)
//组件的回调
/**
 * 通过工号和姓名过滤员工
 * @param {string} query - 通过关键词过滤工人
 * 
 * @returns {void} - 此函数没有返回值。
 */
const filterWorker = (query: string) => {
    workerQuery.value = query.trim();
    if (workerQuery.value && filteredWorkerList.value.length === 1) {
        workerinfo.value = filteredWorkerList.value[0].sWorkerNo;
    }
};
/**
 * 通过机台名称和编号过滤机台
 * 
 * @param {string} query - 机台名称或编号的查询字符串，用于过滤机台列表。
 * 
 * @returns {void} - 此函数没有返回值。
 */
const filterEquipment = (query: string) => {
    equipmentQuery.value = query.trim();
    if (equipmentQuery.value && filteredEquipmentList.value.length === 1) {
        equipmentinfo.value = filteredEquipmentList.value[0].sEquipmentNo;
    }
};
/**
 * 校验工号输入错误后清除
 */
const handleWorkerBlur = () => {
    if (workerinfo.value) {
        const exists = workerList.value.some(
            item => item.sWorkerNo === workerinfo.value
        );
        if (!exists) {
            workerinfo.value = null;
        }
    }
};
/**
 * 校验设备号输入错误后清除
 */
const handleEquipmentBlur = () => {
    if (equipmentinfo.value) {
        const exists = equipmentList.value.some(
            item => item.sEquipmentNo === equipmentinfo.value
        );
        if (!exists) {
            equipmentinfo.value = null;
        }
    }
};
/**
 * 清除当前的设备和工人
 */
const handleProcessChange = () => {
    workerinfo.value = null
    equipmentinfo.value = null
};
/**
 * @param key 
 * 存储选中的工序
 */
const selectProcess = (key) => { selectedProcess.value = key; };


/**
 * 验证用户输入数据的有效性
 * @param {Array<Object>} inputs - 需要验证的输入项数组
 * @param {Object} inputs[] - 输入项对象
 * @param {boolean} inputs[].must - 是否必填项标识
 * @param {string} inputs[].valueType - 输入值类型（'text'/'number'/'select'/'time'）
 * @param {any} inputs[].value - 输入值
 * @param {string} inputs[].label - 输入项显示名称（用于错误提示）
 * @returns {boolean} 验证结果：
 * - true: 所有输入项通过验证
 * - false: 存在未通过的验证项
 * @throws 当验证失败时，会通过 ElMessageBox 弹出错误提示
 * @example
 * // 示例输入项结构
 * validateInputs([{
 *   must: true,
 *   valueType: 'time',
 *   value: '23:59:59',
 *   label: '完成时间'
 * }])
 */
const validateInputs = (inputs) => {
  for (const input of inputs) {
    // 必填验证
    if (input.must && !input.value) {
      ElMessageBox.alert(`${input.label} 不能为空，请填写后再保存。`);
      return false;
    }
    
    // 新增：时间格式验证
    if (input.valueType === 'time' && input.value) {
      const isValid = /^([01]\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(input.value);
      if (!isValid) {
        ElMessageBox.alert(`${input.label} 格式错误，应为 HH:mm:ss`);
        return false;
      }
    }
  }
  return true;
};


/**
 * 将开始和结束阶段的输入数据合并为报工所需的extra对象
 * @param {Array<Object>} beginInputs - 开始阶段的输入数据数组
 * @param {Array<Object>} endInputs - 结束阶段的输入数据数组
 * @param {Object} beginInputs.item - 开始阶段输入项
 * @param {string} beginInputs.item.name - 字段名称
 * @param {string} beginInputs.item.valueType - 值类型（'text'/'number'/'select'/'time'）
 * @param {any} beginInputs.item.value - 字段值
 * @returns {Object} 合并后的extra对象，包含所有有效字段值
 * @throws 当时间格式无效时会在控制台输出错误
 */

const convertInputsToExtra = (beginInputs, endInputs) => {
  const extra = {};

  // 处理开始阶段数据
  beginInputs.forEach(item => {
    if (item.value !== null && item.value !== undefined && item.value !== '') {
      if (item.valueType === 'time') {
        if (/^([01]\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(item.value)) {
          extra[item.name] = item.value;
        } else {
          console.error('时间格式错误:', item.value);
        }
      } else {
        extra[item.name] = item.value;
      }
    }
  });

  // 处理结束阶段数据（覆盖同名项）
  endInputs.forEach(item => {
    if (item.value !== null && item.value !== undefined && item.value !== '') {
      if (item.valueType === 'time') {
        if (/^([01]\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(item.value)) {
          extra[item.name] = item.value;
        } else {
          console.error('时间格式错误:', item.value);
        }
      } else {
        extra[item.name] = item.value;
      }
    }
  });

  return extra;
};




/**
 * 保存输入数据的主流程处理
 * @param {Array<Object>} currentInputs - 当前需要保存的输入项数组
 * @param {boolean} [isEnd=false] - 是否为结束阶段保存
 * @returns {void}
 * @throws 以下情况会弹出警告框：
 *   - 员工号/机台号未填
 *   - 输入验证失败
 *   - 工卡号为空
 * @description 处理逻辑：
 *   1. 结束阶段保存时合并验证所有输入项
 *   2. 开始阶段保存到localStorage
 *   3. 结束阶段提交后清除localStorage
 */
const saveInputs = (currentInputs, isEnd = false) => {
  focus();
  if (!workerinfo.value || !equipmentinfo.value) {
    ElMessageBox.alert(`请填写员工号和机台号`);
    return;
  }

  // 统一验证逻辑
  if (isEnd) {
    // 合并所有输入项进行验证
    const allInputs = [...currentBeginInputs.value, ...currentInputs];
    if (!validateInputs(allInputs)) {
      return;
    }
  } else {
    if (!validateInputs(currentInputs)) {
      return;
    }
  }

  if (!workCardNumberCopy.value) {
    ElMessageBox.alert("工卡号不能为空，无法保存数据");
    return;
  }

  if (isEnd) {
    // 合并当前输入的开始和结束数据
    const extra = convertInputsToExtra(currentBeginInputs.value, currentInputs);
    productReport(extra);
    
    // 清除本地存储
    localStorage.removeItem(`${workCardNumberCopy.value}_${selectedProcess.value}`);
  } else {
    localStorage.setItem(`${workCardNumberCopy.value}_${selectedProcess.value}`, JSON.stringify(currentInputs));
    ElMessageBox.alert("已经保存").then(() => {
      focus();
    });
  }
};
/**
 * 页面加载物料号工卡信息
 */
const getMaterialAndGkInfo = () => {
    if (workCardNumber.value) {
        try {
            gkInfo(workCardNumber.value).then((val) => {
                if (val.data.success) {
                    bCompleted.value = val.data.response.bCompleted
                    materialInfo.value = val.data.response;
                } else {
                    ElMessageBox.alert(val.data.msg).then(() => {
                    });
                }
            }).catch(error => {
                ElMessageBox.alert(JSON.stringify(error)).then(() => {
                });
            }).finally(() => {
                workCardNumberCopy.value = workCardNumber.value
                workCardNumber.value = null;
            });
        } catch (error) {
            ElMessageBox.alert("获取工卡错误").then(() => {
            });
        }
    } else {
        ElMessageBox.alert("工卡号不能为空").then(() => {
        });
    }
};
//主流程
/**
 * 报工
 */
const productReport = (extra = {}) => {
    console.log('DEBUG - extra数据:', JSON.parse(JSON.stringify(extra))); 
    gkTrackInfo.value.sCardNo = workCardNumberCopy.value//工卡号
    gkTrackInfo.value.GXNo = selectedProcess.value//工序编号
    gkTrackInfo.value.tFactStartTime = formatTime(Date.now(), 'yyyy-MM-dd HH:mm:ss')//开始时间
    gkTrackInfo.value.tFactEndTime = formatTime(Date.now(), 'yyyy-MM-dd HH:mm:ss')//结束时间
    gkTrackInfo.value.sEquipmentNo = selectedEquipment.value.sEquipmentNo//设备编号
    gkTrackInfo.value.nTrackQty = Number(materialInfo.value.mic)//开卡量
    gkTrackInfo.value.nTrackQtyEx = Number(materialInfo.value.ps)
    gkTrackInfo.value.sWorkerGroupNo = selectedWorker.value.sWorkerGroupNo//班组号
    gkTrackInfo.value.sWorkerGroupName = selectedWorker.value.sWorkerGroupName//班组名称
    gkTrackInfo.value.sWorkerNoList = selectedWorker.value.sWorkerNo//工号
    gkTrackInfo.value.sWorkerNameList = selectedWorker.value.sWorkerName//姓名
    gkTrackInfo.value.sUserID = ""//操作人ID
    gkTrackInfo.value.sRemark = ""//备注
    gkTrackInfo.value.sUnionCardNoList = ""//并卡工卡号
    gkTrackInfo.value.sLocation = ""//不用传递nTrackQtyEx
    gkTrackInfo.value.extra = extra
    console.log("DEBUG - 提交数据:", JSON.stringify(gkTrackInfo.value, null, 2));

    gkTrack(gkTrackInfo.value)
    .then((response) => {
      // 防御性检查：确保响应存在且数据有效
      if (!response || !response.data) {
        throw new Error("API 返回空数据");
      }

      // 兼容不同结构的响应（如直接返回 success 在顶层）
      const apiData = response.data;
      if (apiData.success) {
        // 成功时更新工卡信息
        return gkInfo(workCardNumberCopy.value);
      } else {
        throw new Error(apiData.msg || "报工失败（业务逻辑错误）");
      }
    })
    .then((gkInfoResponse) => {
      // 处理工卡信息更新
      if (gkInfoResponse?.data?.success) {
        bCompleted.value = gkInfoResponse.data.response.bCompleted;
        materialInfo.value = gkInfoResponse.data.response;
        ElMessageBox.alert("报工成功");
      } else {
        throw new Error("更新工卡信息失败");
      }
    })
    .catch((error) => {
      // 统一错误处理逻辑
      let errorMessage = "未知错误";
      if (error.response) {
        // Axios 捕获的 HTTP 错误（如 4xx/5xx）
        errorMessage = `服务器错误: ${error.response.status}`;
      } else if (error.message) {
        // 自定义抛出的错误
        errorMessage = error.message;
      }
      console.error("报工失败详情:", error);
      ElMessageBox.alert(`报工失败: ${errorMessage}`);
    });
};
//计算属性
/**
 * 工人清单
 */
const filteredWorkerList = computed(() => {
    return workerList.value.filter(item =>
        item.sWorkerName?.includes(workerQuery.value) ||
        item.sWorkerNo?.includes(workerQuery.value)
    );
});
/**
 * 设备清单
 */
const filteredEquipmentList = computed(() => {
    return equipmentList.value.filter(item =>
        item.sEquipmentName?.includes(equipmentQuery.value) ||
        item.sEquipmentNo?.includes(equipmentQuery.value)
    );
});
/**
 * 选中的工人对象
 **/
const selectedWorker = computed(() => {
    return filteredWorkerList.value.find(item => item.sWorkerNo === workerinfo.value) || null;
});
/**
 * 选中的设备对象
 */
const selectedEquipment = computed(() => {
    return filteredEquipmentList.value.find(item => item.sEquipmentNo === equipmentinfo.value) || null;
});
const focus = async () => {
    await nextTick(); // 等待 DOM 更新
    if (workCardInput.value) {
        const inputElement = workCardInput.value.$el.querySelector('input'); // 获取实际的 input 元素
        if (inputElement) {
            inputElement.focus(); // 聚焦输入框
        }
    }
}
/**
 * 监听
 */
/**
 * 输入工卡后自动选工序
 */
watch(() => materialInfo.value, (newNextGxCode) => {
    const data = []
    for (let index = 0; index < newNextGxCode.gylx.length; index++) {
        const element: any = newNextGxCode.gylx[index];
        data.push({ "key": element.gxbm, "name": element.gxmc })
    }
    filteredProcesses.value = data
    // 检查 newNextGxCode 是否存在，并且在 filteredProcesses 中是否有对应的工序
    if (newNextGxCode) {
        const matchingProcess = filteredProcesses.value.find(item => item.key === newNextGxCode.nextGxCode);
        if (matchingProcess) {
            console.log(newNextGxCode, 3);
            selectedProcess.value = newNextGxCode.nextGxCode; // 自动选择工序
        }
    }
});
/**
 * 切换工序自动清理工人和设备
 */
watch([selectedProcess], () => {
    handleProcessChange()
});
/**
 * 根据工卡号和工序加载浏览器缓存里保存的个性化表单数据
 */
watch([workCardNumber, selectedProcess], ([newWorkCardNumber, newSelectedProcess]) => {
    // 检查两个变量是否都不为空（包括空字符串）
    if (newWorkCardNumber && newSelectedProcess) {
        const storedData = localStorage.getItem(`${newWorkCardNumber}_${newSelectedProcess}`);
        if (storedData) {
            const parsedData = JSON.parse(storedData);
            currentBeginInputs.value = parsedData;
        }
    }
});
/**
 * 切换工序的时候重新加载的工人、设备清单，重新加载个性化表单的字段
 */
watch([selectedProcess], ([newSelectedProcess]) => {
    if (newSelectedProcess) {
        GetWorkerAndEquipmentByProcedure(newSelectedProcess).then(res => {
            if (res.data.success) {
                workerList.value = res.data.response.workerinfo;
                equipmentList.value = res.data.response.equipmentinfo;
            }
        });
    }
    if (newSelectedProcess && content[newSelectedProcess]) {
        const processContent = content[newSelectedProcess];
        currentBeginInputs.value = processContent.begin.input;
        currentEndInputs.value = processContent.end ? processContent.end.input : null;
    } else {
        currentBeginInputs.value = [];
        currentEndInputs.value = null;
    }
});
// 在组件挂载后聚焦输入框
onMounted(focus);
</script>
<style scoped lang="scss" src="../style.scss"></style>