<script setup lang="ts">
import { ref, defineEmits } from 'vue'
import * as XLSX from 'xlsx'
import { HRAnalyzer } from '@/utils/hrUtils'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['update'])

function sendData(val) {
    emit('update', { val })
}

/**
 * 分析结果响应式引用
 * @type {import('vue').Ref<object|null>}
 */
const analysisResult = ref(null)

/**
 * HR分析器实例引用
 * @type {import('vue').Ref<HRAnalyzer|null>}
 */
const hrAnalyzer = ref(null)

/**
 * 公司总览数据引用
 * @type {import('vue').Ref<object|null>}
 */
const companyOverview = ref(null)

/**
 * 部门过滤输入字符串（逗号分隔）
 * @type {import('vue').Ref<string>}
 */
const departmentsInput = ref('')

/**
 * 年龄分段输入字符串（逗号分隔数字）
 * @type {import('vue').Ref<string>}
 * @example "18,25,30,40,50,60"
 */
const ageRangesInput = ref('18,25,30,40,50,60')

/**
 * 工龄分段输入字符串（逗号分隔数字）
 * @type {import('vue').Ref<string>}
 * @example "0,1,3,5,10,25"
 */
const seniorityInput = ref('0,1,3,5,10,25')

const leaveSeniorityInput = ref('0,0.2,1,3,5,10,25')
/**
 * 当前选中的分析类型
 * @type {import('vue').Ref<string>}
 * @validValues 
 * ['seniorityAnalysis', 'ageAnalysis', 'educationAnalysis', 
 *  'marriageAnalysis', 'resignSeniorityAnalysis', 'resignTypeAnalysis',
 *  'companyEmployees', 'resignRateAnalysis']
 */
const selectedAnalysis = ref('seniorityAnalysis')

/**
 * 解析部门输入字符串为数组
 * @param {string} input - 逗号分隔的部门字符串
 * @returns {string[]} 处理后的部门数组
 * @description
 * - 自动去除空格
 * - 过滤空字符串
 * - 示例：" 技术部, 销售部 " → ["技术部", "销售部"]
 */
function parseDepartments(input: string): string[] {
    return input.split(',').map(s => s.trim()).filter(s => s.length > 0)
}

const now = ref(new Date())



/**
 * 解析年龄范围输入字符串为排序后的数字数组
 * @param {string} input - 逗号分隔的数字字符串
 * @returns {number[]} 升序排列的有效数字数组
 * @description
 * - 自动过滤无效数字
 * - 自动排序
 * - 示例："30, 25, abc" → [25, 30]
 */
function parseNumRanges(input: string): number[] {
    return input.split(',')
        .map(s => parseFloat(s.trim()))
        .filter(n => !isNaN(n))
        .sort((a, b) => a - b)
}
const headcountData = ref<Array<{ 中心: string, 课: string, 编制人数: number }>>([])
/**
 * 处理Excel文件上传事件
 * @param {File} file - 上传的文件对象
 * @description 执行流程：
 * 1. 读取上传的Excel文件
 * 2. 转换为JSON数据
 * 3. 初始化HRAnalyzer分析器
 * 4. 重置分析结果
 */
function handleFileChange(file: File) {
    if (!file) return

    const reader = new FileReader()
    reader.onload = (evt) => {
        const arrayBuffer = evt.target?.result as ArrayBuffer
        const data = new Uint8Array(arrayBuffer)

        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        // 读取第二个页签“编制人数”
        const secondSheetName = '编制人数'
        const worksheet2 = workbook.Sheets[secondSheetName]
        let headcountJson = []
        if (worksheet2) {
            headcountJson = XLSX.utils.sheet_to_json(worksheet2)
        } else {
            ElMessage.warning('未找到“编制人数”页签，编制人数统计将不准确')
        }
        hrAnalyzer.value = new HRAnalyzer(jsonData)
        headcountData.value = headcountJson.map(item => ({
            中心: item['中心']?.toString().trim() || '',
            课: item['课']?.toString().trim() || '',
            编制人数: Number(item['编制人数']) || 0
        }))
        analysisResult.value = null
        companyOverview.value = null
        ElMessage.success('Excel文件导入成功')
    }
    reader.readAsArrayBuffer(file)
}

/**
 * 执行选定的分析类型
 * @description 根据selectedAnalysis的值调用对应的分析方法
 * @throws 当未初始化HRAnalyzer时弹出警告
 * @sideeffects 更新analysisResult或companyOverview的值
 */
function runAnalysis() {
    if (!hrAnalyzer.value) {
        ElMessage.warning('请先导入Excel文件')
        return
    }
    const departments = parseDepartments(departmentsInput.value)
    const ageRanges = parseNumRanges(ageRangesInput.value)
    const seniorityRanges = parseNumRanges(seniorityInput.value)

    const leaveSeniorityRanges = parseNumRanges(leaveSeniorityInput.value)

    analysisResult.value = null
    companyOverview.value = null
    const ym = `${now.value.getFullYear()}-${(now.value.getMonth() + 1).toString().padStart(2, '0')}`
    const seniorityAnalysis = hrAnalyzer.value.seniorityAnalysis(departments, seniorityRanges)
    const ageAnalysis = hrAnalyzer.value.ageAnalysis(departments, ageRanges)
    const educationAnalysis = hrAnalyzer.value.educationAnalysis(departments)
    const marriageAnalysis = hrAnalyzer.value.marriageAnalysis(departments)
    const resignSeniorityAnalysis = hrAnalyzer.value.resignSeniorityAnalysis(departments, leaveSeniorityRanges)
    const resignTypeAnalysis = hrAnalyzer.value.resignTypeAnalysis(departments)
    const companyEmployees = companyOverview.value = hrAnalyzer.value.companyEmployees(departments, ym, headcountData.value)
    const resignRateAnalysis = analysisResult.value = hrAnalyzer.value.resignRateAnalysis(departments, now.value)
    demo = {
        seniorityAnalysis, ageAnalysis, educationAnalysis, marriageAnalysis, resignSeniorityAnalysis, resignTypeAnalysis, companyEmployees, resignRateAnalysis
    }
    // switch (selectedAnalysis.value) {
    //     case 'seniorityAnalysis':
    //         analysisResult.value = hrAnalyzer.value.seniorityAnalysis(departments, seniorityRanges)
    //         break
    //     case 'ageAnalysis':
    //         analysisResult.value = hrAnalyzer.value.ageAnalysis(departments, ageRanges)
    //         break
    //     case 'educationAnalysis':
    //         analysisResult.value = hrAnalyzer.value.educationAnalysis(departments)
    //         break
    //     case 'marriageAnalysis':
    //         analysisResult.value = hrAnalyzer.value.marriageAnalysis(departments)
    //         break
    //     case 'resignSeniorityAnalysis':
    //         analysisResult.value = hrAnalyzer.value.resignSeniorityAnalysis(departments, ageRanges)
    //         break
    //     case 'resignTypeAnalysis':
    //         analysisResult.value = hrAnalyzer.value.resignTypeAnalysis(departments)
    //         break
    //     case 'companyEmployees':
    //         const ym = `${now.value.getFullYear()}-${(now.value.getMonth() + 1).toString().padStart(2, '0')}`
    //         companyOverview.value = hrAnalyzer.value.companyEmployees(departments, ym)
    //         break
    //     case 'resignRateAnalysis':
    //         analysisResult.value = hrAnalyzer.value.resignRateAnalysis(departments)
    //         break
    //     default:
    //         ElMessage.error('未知分析方法')
    // }
    sendData(demo)
}

/**
 * 格式化后的分析结果计算属性
 * @type {ComputedRef<string>}
 * @description 依赖analysisResult的值，使用formattedResult进行格式化
 */
let demo = null
</script>

<template>
    <el-card shadow="hover" class="container">
        <h3>导入Excel文件</h3>
        <el-upload class="upload-demo" drag :show-file-list="false"
            :before-upload="(file) => { handleFileChange(file); return false; }" accept=".xlsx,.xls">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">仅支持Excel文件(.xls, .xlsx)</div>
        </el-upload>

        <el-divider></el-divider>

        <el-form label-position="top" label-width="120px" class="form">
            <el-form-item label="部门列表（逗号分隔）">
                <el-input v-model="departmentsInput" placeholder="例如：研发课,工艺课" clearable />
            </el-form-item>

            <el-form-item label="年龄区间（逗号分隔）">
                <el-input v-model="ageRangesInput" placeholder="例如：18,25,30,40,50,60" clearable />
            </el-form-item>

            <el-form-item label="工龄区间（逗号分隔）">
                <el-input v-model="seniorityInput" placeholder="例如：0,1,3,5,10,25" clearable />
            </el-form-item>

            <el-form-item label="离职工龄区间（逗号分隔）">
                <el-input v-model="leaveSeniorityInput" placeholder="例如：0,1,3,5,10,25" clearable />
            </el-form-item>


            <el-form-item label="统计时间">
                <el-date-picker v-model="now" type="date" placeholder="选择日期" style="width: 100%;" />
            </el-form-item>

            <!-- <el-form-item label="选择分析方法">
                <el-select v-model="selectedAnalysis" placeholder="请选择分析方法" clearable>
                    <el-option label="工龄分析" value="seniorityAnalysis" />
                    <el-option label="年龄分析" value="ageAnalysis" />
                    <el-option label="学历分析" value="educationAnalysis" />
                    <el-option label="婚姻分析" value="marriageAnalysis" />
                    <el-option label="离职工龄分析" value="resignSeniorityAnalysis" />
                    <el-option label="离职类别分析" value="resignTypeAnalysis" />
                    <el-option label="公司人员总览" value="companyEmployees" />
                    <el-option label="离职率分析" value="resignRateAnalysis" />
                </el-select>
            </el-form-item> -->

            <el-form-item>
                <el-button type="primary" @click="runAnalysis">运行分析</el-button>
            </el-form-item>
        </el-form>

        <el-divider></el-divider>

        <!-- <div v-if="analysisResult" class="result-section">
            <h3>分析结果（JSON格式）</h3>
            <el-card class="result-card" shadow="never">
                <pre>{{ demo }}</pre>
            </el-card>
        </div> -->

        <div v-if="companyOverview" class="result-section">
            <h3>公司人员总览</h3>
            <el-descriptions border size="small">
                <el-descriptions-item label="总人数">{{ companyOverview.total }}</el-descriptions-item>
                <el-descriptions-item label="编制人数">{{ companyOverview.headcount }}</el-descriptions-item>
                <el-descriptions-item label="当月入职人数">{{ companyOverview.entryNumberThisMonth }}</el-descriptions-item>
                <el-descriptions-item label="当年入职人数">{{ companyOverview.entryNumberThisYear }}</el-descriptions-item>
                <el-descriptions-item label="当月离职人数">{{ companyOverview.resignNumberThisMonth }}</el-descriptions-item>
                <el-descriptions-item label="当年离职人数">{{ companyOverview.resignNumberThisYear }}</el-descriptions-item>
            </el-descriptions>
        </div>
    </el-card>
</template>

<style scoped>
.container {
    max-width: 720px;
    margin: 20px auto;
    padding: 20px;
}

.upload-demo {
    width: 100%;
    margin-bottom: 20px;
}

.form {
    margin-bottom: 20px;
}

.result-section {
    margin-top: 20px;
}

.result-card {
    background-color: #f9f9f9;
    padding: 15px;
    max-height: 400px;
    overflow: auto;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: monospace;
    font-size: 13px;
}
</style>
