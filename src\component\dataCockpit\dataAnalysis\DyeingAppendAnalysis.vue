<template>
  <div class="page-container">
    <div class="chart-card">
      <div class="chart-header">
        <div class="chart-title">染色追加分析看板</div>
        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            :show-file-list="false"
            :before-upload="handleFile"
            accept=".xlsx,.xls"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">拖拽或点击上传Excel</div>
          </el-upload>
        </div>
      </div>
      <div class="chart-container" ref="containerRef">
        <ECharts v-if="chartData.length" :option="chartOptions" class="echarts-for-vue" :width="containerWidth" :height="containerHeight" />
        <div v-else class="no-data">请上传Excel文件，或数据为空</div>
      </div>
      <div class="footer">
        <p>数据更新时间: {{ new Date().toLocaleDateString() }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import ECharts from '@/echarts/index.vue'
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, onUnmounted, ref } from 'vue'
import * as XLSX from 'xlsx'

const chartData = ref([])
const chartOptions = ref({})

// 新增：自适应宽高
const containerRef = ref(null)
const containerWidth = ref(0)
const containerHeight = ref(0)

function updateContainerSize() {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth
    containerHeight.value = containerRef.value.offsetHeight
  }
}

onMounted(() => {
  updateContainerSize()
  window.addEventListener('resize', updateContainerSize)
})
onUnmounted(() => {
  window.removeEventListener('resize', updateContainerSize)
})
// Excel上传后也要刷新一次
function afterExcelUpdate() {
  nextTick(() => {
    updateContainerSize()
  })
}

function handleFile(file) {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheet = workbook.Sheets[workbook.SheetNames[0]]
      const json = XLSX.utils.sheet_to_json(sheet)
      if (!json.length) {
        ElMessage.warning('Excel数据为空')
        chartData.value = []
        chartOptions.value = {}
        afterExcelUpdate()
        return
      }
      processData(json)
      ElMessage.success('Excel上传并解析成功')
      afterExcelUpdate()
    } catch (err) {
      ElMessage.error('Excel解析失败，请检查文件格式')
      chartData.value = []
      chartOptions.value = {}
      afterExcelUpdate()
    }
  }
  reader.readAsArrayBuffer(file)
  return false
}

function processData(data) {
  // 字段兼容：支持中英文、去除空格
  function getField(row, keys) {
    for (const k of keys) {
      if (row[k] !== undefined) return String(row[k]).trim()
    }
    return ''
  }
  const group = {}
  data.forEach(row => {
    const code = getField(row, ['厂商'])
    if (!code) return
    const isAppend = getField(row, ['是否追加', '追加', 'IsAppend', 'isAppend'])
    if (!group[code]) group[code] = { total: 0, append: 0 }
    group[code].total += 1
    if (isAppend === '是' || isAppend.toLowerCase() === 'yes' || isAppend === '1') group[code].append += 1
  })
  const arr = Object.entries(group).map(([code, { total, append }]) => ({
    code,
    total,
    append,
    rate: total ? append / total : 0
  }))
  arr.sort((a, b) => b.total - a.total)
  const top15 = arr.slice(0, 15)
  top15.sort((a, b) => b.rate - a.rate)
  chartData.value = top15
  if (!top15.length) {
    chartOptions.value = {}
    ElMessage.warning('未找到有效数据，请检查Excel内容和表头！')
    return
  }
  // 构造与Top15Chart一致的数据结构
  const items = top15.map(d => d.code)
  const batches = top15.map(d => d.total)
  const repairRates = top15.map(d => +(d.rate * 100).toFixed(1))
  chartOptions.value = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: '#ddd',
      borderWidth: 1,
      padding: 15,
      textStyle: {
        color: '#333',
        fontSize: 14
      },
      formatter: function(params) {
        const batchParam = params[0];
        const rateParam = params[1];
        return `
          <div style="margin-bottom:8px;font-size:16px;font-weight:bold;color:#333">${batchParam.name}</div>
          <div style="display:flex;align-items:center;margin-bottom:5px;">
            <div style="width:12px;height:12px;background:#2c6bcd;margin-right:8px;border-radius:2px;"></div>
            <span style="color:#666;font-size:14px;">生产总缸数: </span>
            <span style="font-weight:bold;font-size:16px;margin-left:5px;color:#2c6bcd;">${batchParam.value}</span>
          </div>
          <div style="display:flex;align-items:center;">
            <div style="width:12px;height:12px;background:#ee6666;margin-right:8px;border-radius:2px;"></div>
            <span style="color:#666;font-size:14px;">追加率: </span>
            <span style="font-weight:bold;font-size:16px;margin-left:5px;color:#ee6666;">${rateParam.value}%</span>
          </div>
        `;
      }
    },
    legend: {
      data: ['生产总缸数', '追加率'],
      bottom: 10,
      itemGap: 30,
      textStyle: {
        fontSize: 14
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: items,
      axisLine: {
        lineStyle: {
          color: '#606266',
          width: 1
        }
      },
      axisLabel: {
        interval: 0,
        rotate: 45,
        fontSize: 12,
        fontWeight: 'bold'
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '生产总缸数',
        nameTextStyle: {
          color: '#2c6bcd',
          fontSize: 14,
          fontWeight: 'bold',
          padding: [0, 50, 0, 0]
        },
        axisLine: {
          lineStyle: {
            color: '#2c6bcd'
          }
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        },
        min: 0,
        max: value => Math.ceil(value.max * 1.1)
      },
      {
        type: 'value',
        name: '追加率',
        nameTextStyle: {
          color: '#ee6666',
          fontSize: 14,
          fontWeight: 'bold',
          padding: [0, 0, 0, 50]
        },
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#ee6666'
          }
        },
        axisLabel: {
          formatter: '{value}%',
          fontSize: 12
        },
        splitLine: { 
          show: false
        }
      }
    ],
    series: [
      {
        name: '生产总缸数',
        type: 'bar',
        barWidth: '40%',
        data: batches,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#5d9cfc' },
              { offset: 0.5, color: '#2c6bcd' },
              { offset: 1, color: '#1a4c8c' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 12,
          color: '#2c6bcd',
          fontWeight: 'bold'
        }
      },
      {
        name: '追加率',
        type: 'line',
        symbolSize: 8,
        yAxisIndex: 1,
        data: repairRates,
        lineStyle: {
          width: 3,
          color: '#ee6666'
        },
        itemStyle: {
          color: '#ee6666',
          borderWidth: 2,
          borderColor: '#fff'
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          fontSize: 12,
          color: '#ee6666',
          fontWeight: 'bold'
        },
        z: 10
      }
    ]
  }
  // 调试输出
  console.log('分组结果', group)
  console.log('Top15', top15)
}
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保它占据父元素的全部高度 */
}
.chart-card {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column; /* 让chart-card成为flex列容器 */
  flex: 1; /* 让chart-card自动撑满page-container剩余空间 */
  margin: 0 auto;
  max-width: 1200px;
  width: 100%;
  .chart-header {
    padding: 20px 20px 15px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(to right, #f9fbff, #ffffff);
    flex: 0 0 auto; /* 头部固定高度 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    .chart-title {
      font-size: 18px;
      font-weight: bold;
      color: #1a1a1a;
      margin-bottom: 5px;
      position: relative;
      padding-left: 15px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: #3366ff;
        border-radius: 2px;
      }
    }
    .chart-subtitle {
      font-size: 14px;
      color: #666;
      padding-left: 15px;
    }
  }
  .chart-container {
    flex: 1; /* 撑满 chart-card 剩余高度 */
    padding: 0;
    width: 100%;
    min-height: 0; /* 避免flex item内容溢出，确保flex:1正常工作 */
    display: flex; /* 让chart-container成为flex容器 */
    flex-direction: column; /* 让echarts和no-data垂直堆叠 */
    
    .no-data {
      flex: 1; /* 让 no-data 填满 chart-container */
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 16px;
    }
  }
}
.footer {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 12px;
  flex-shrink: 0; /* 防止 footer 被压缩 */
}
</style> 