// 基础样式
:root {
  --primary-color: #3366ff;
  --danger-color: #ee6666;
  --text-color: #303133;
  --text-secondary: #606266;
  --border-color: #ebeef5;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
  color: var(--text-color);
}

// 图表容器样式
.chart-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-top: 24px;
}

// 响应式布局
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;

    .el-date-editor {
      width: 100%;
    }

    .filter-btn {
      margin-top: 0;
      width: 100%;
    }
  }
}