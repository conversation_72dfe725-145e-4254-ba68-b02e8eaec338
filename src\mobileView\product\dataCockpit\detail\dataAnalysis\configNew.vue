<template>
  <div class="analysis-container">
    <!-- 色号分析组 -->
    <AnalysisChartGroup
      :raw-data="filteredData"
      top15-title="TOP15色号回修率分析"
      top15-subtitle="按色号统计回修率和染色缸数"
      :top15-data="colorTop15Data"
      monthly-title="色号月度回修率分析"
      data-type="色号"
      data-field="colorCode"
      v-model:start-date="startDate"
      v-model:end-date="endDate"
      v-model:tank-type="tankTypeFilter"
    />
  </div>
</template>

<script setup>

import { getAnalysisData, transformApiDataToFrontend } from '@/api/dataAnalysis/dyeingAnalysis';
import AnalysisChartGroup from '@/component/dataCockpit/dataAnalysis/AnalysisChartGroup.vue';
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref } from 'vue';



// 状态管理
const rawData = ref([]);
const startDate = ref('');
const endDate = ref('');

// 缸型筛选状态
const tankTypeFilter = ref('all'); // 'all', 'large', 'small'

// 过滤后的数据（应用日期和缸型筛选）
const filteredData = computed(() => {
  if (!rawData.value || rawData.value.length === 0) {
    return [];
  }

  let filtered = rawData.value;

  // 应用日期筛选
  if (startDate.value || endDate.value) {
    filtered = filtered.filter(item => {
      if (!item.dyeDate) return false;

      const dyeDate = new Date(item.dyeDate);
      if (isNaN(dyeDate)) return false;

      const start = startDate.value ? new Date(startDate.value) : new Date(0);
      const end = endDate.value ? new Date(endDate.value) : new Date();

      return dyeDate >= start && dyeDate <= end;
    });
  }

  // 应用缸型筛选
  if (tankTypeFilter.value !== 'all') {
    filtered = filtered.filter(item => {
      const capacity = parseFloat(item.batchCapacity) || 0;
      if (tankTypeFilter.value === 'large') {
        return capacity >= 250;
      } else if (tankTypeFilter.value === 'small') {
        return capacity < 250;
      }
      return true;
    });
  }

  return filtered;
});

// 计算属性 - 色号TOP15数据
const colorTop15Data = computed(() => {
  if (!filteredData.value || filteredData.value.length === 0) {
    return { items: [], batches: [], repairRates: [] };
  }

  // 按色号分组统计
  const colorStats = {};
  filteredData.value.forEach(item => {
    if (item.colorCode) {
      if (!colorStats[item.colorCode]) {
        colorStats[item.colorCode] = { total: 0, repairs: 0 };
      }
      colorStats[item.colorCode].total++;
      if (item.result === 0) {
        colorStats[item.colorCode].repairs++;
      }
    }
  });

  // 计算回修率并排序
  const colorArray = Object.entries(colorStats).map(([color, stats]) => ({
    color,
    batches: stats.total,
    repairRate: stats.total > 0 ? (stats.repairs / stats.total * 100) : 0
  }));

  // 按总缸数降序排序，取前15个（TOP15按产量排序）
  const top15ByVolume = colorArray
    .sort((a, b) => b.batches - a.batches)
    .slice(0, 15);

  // 然后按回修率降序排序显示
  const sortedByRepairRate = top15ByVolume
    .sort((a, b) => b.repairRate - a.repairRate);

  return {
    items: sortedByRepairRate.map(item => item.color),
    batches: sortedByRepairRate.map(item => item.batches),
    repairRates: sortedByRepairRate.map(item => parseFloat(item.repairRate.toFixed(1)))
  };
});









// 从API加载数据
const loadDataFromAPI = async (params = {}) => {
  try {
    const response = await getAnalysisData(params);

    if (response.code === 0 && Array.isArray(response.data)) {
      const transformedData = transformApiDataToFrontend(response.data);



      rawData.value = transformedData;
      ElMessage.success(`色号分析已加载${transformedData.length}条数据`);
    } else {
      throw new Error(response.msg || '获取数据失败');
    }
  } catch (error) {
    ElMessage.error(`加载数据失败: ${error.message}`);
  }
};



// 组件挂载时自动加载API数据
onMounted(async () => {
  // 默认加载最近1年的数据
  const endDate = new Date();
  const startDate = new Date();
  startDate.setFullYear(startDate.getFullYear() - 1);

  await loadDataFromAPI({
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  });
});

</script>

<style scoped lang="scss">
.analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
}


</style>
