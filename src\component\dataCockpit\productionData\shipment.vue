<template>
    <div>
        <div class="title-style" :style="{ backgroundColor: titleColor }">
            <h3>{{ title }}</h3>
        </div>
        <table border="1" cellspacing="0" cellpadding="5">
            <thead>
                <tr>
                    <th>{{ smallTitle.firstTitle }}</th>
                    <th>{{ smallTitle.secondTitle }}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="total">{{ data.one }}</td>
                    <td style="font-weight: bolder;font-size: large;">{{ data.two }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { DataProps } from './interface';

const props = defineProps({
    title: {
        type: String,
        required: false,
        default: '四、出货情况'
    },
    data: {
        type: Object as PropType<DataProps>,
        required: false,
        default: () => ({ todayOrder: 0, monthOrder: 0 })
    },
    titleColor: {
        type: String,
        required: false,

    },
    smallTitle: {
        type: Object,
        required: false,
        default: () => ({
            firstTitle: '当日出货(单位：吨)',
            secondTitle: '当月出货(单位：吨)'
        })
    }

})

const title = props.title ?? '四、出货情况';
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>