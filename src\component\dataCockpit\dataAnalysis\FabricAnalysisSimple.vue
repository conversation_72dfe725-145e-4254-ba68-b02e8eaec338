<template>
  <div class="fabric-analysis-simple">
    <h2>面料分析 - 简化版</h2>
    
    <!-- 状态显示 -->
    <div class="status-section">
      <el-card>
        <h3>当前状态</h3>
        <p>数据条数: {{ rawData.length }}</p>
        <p>加载状态: {{ loading ? '加载中...' : '已完成' }}</p>
        <p>API状态: {{ apiStatus }}</p>
      </el-card>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="testAPI" :loading="loading">
        测试API连接
      </el-button>
      <el-button type="success" @click="loadMockData">
        加载模拟数据
      </el-button>
      <el-button type="warning" @click="clearData">
        清空数据
      </el-button>
    </div>

    <!-- 数据显示 -->
    <div v-if="rawData.length > 0" class="data-section">
      <el-card>
        <h3>数据预览 (前5条)</h3>
        <el-table :data="rawData.slice(0, 5)" style="width: 100%">
          <el-table-column prop="colorCode" label="色号" width="100" />
          <el-table-column prop="dyeDate" label="染色日期" width="120" />
          <el-table-column prop="decathlonFabric" label="迪卡侬面料" width="200" />
          <el-table-column prop="deweiFabric" label="得伟面料" width="200" />
          <el-table-column prop="result" label="结果" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.result === 1 ? 'success' : 'danger'">
                {{ scope.row.result === 1 ? '合格' : '回修' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="no-data-section">
      <el-empty description="暂无数据">
        <el-button type="primary" @click="testAPI">加载数据</el-button>
      </el-empty>
    </div>

    <!-- 错误日志 -->
    <div v-if="errorLog.length > 0" class="error-section">
      <el-card>
        <h3>错误日志</h3>
        <div v-for="(error, index) in errorLog" :key="index" class="error-item">
          <strong>{{ error.time }}:</strong> {{ error.message }}
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 状态管理
const rawData = ref([])
const loading = ref(false)
const apiStatus = ref('未测试')
const errorLog = ref([])

// 添加错误日志
const addError = (message) => {
  errorLog.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (errorLog.value.length > 5) {
    errorLog.value = errorLog.value.slice(0, 5)
  }
}

// 测试API连接
const testAPI = async () => {
  loading.value = true
  apiStatus.value = '测试中...'
  
  try {
    console.log('开始测试API连接...')
    
    // 使用fetch直接测试API
    const response = await fetch('http://*************:18090/api/data/invoke/1/analysis-data/0?api_secret=DRyEnruYbgYOGbiw&startDate=2025-01-01&endDate=2025-01-31')
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    console.log('API响应:', data)
    
    if (data.code === 0 && Array.isArray(data.data)) {
      // 转换数据格式
      const transformedData = data.data.map(item => ({
        id: Number(item.ID) || 0,
        colorCode: item.colorCode || '',
        dyeDate: item.dyeDate ? item.dyeDate.split(' ')[0] : '',
        decathlonFabric: item.decathlonFabric || '',
        deweiFabric: item.deweiFabric || '',
        result: item.result === "1" || item.result === 1 ? 1 : 0,
        year: Number(item.year) || new Date().getFullYear(),
        month: Number(item.month) || new Date().getMonth() + 1
      }))
      
      rawData.value = transformedData
      apiStatus.value = `成功 - ${transformedData.length}条数据`
      ElMessage.success(`API连接成功！获取到${transformedData.length}条数据`)
    } else {
      throw new Error(`API返回格式错误: code=${data.code}`)
    }
    
  } catch (error) {
    console.error('API测试失败:', error)
    apiStatus.value = `失败 - ${error.message}`
    addError(`API测试失败: ${error.message}`)
    ElMessage.error(`API连接失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 加载模拟数据
const loadMockData = () => {
  const mockData = [
    {
      id: 1,
      colorCode: "G21A",
      dyeDate: "2025-01-15",
      decathlonFabric: "BASIC LINING BR REC",
      deweiFabric: "T550180-84圈绒（环保纱版）",
      result: 1,
      year: 2025,
      month: 1
    },
    {
      id: 2,
      colorCode: "G21A",
      dyeDate: "2025-01-15",
      decathlonFabric: "BASIC LINING BR REC",
      deweiFabric: "T550180-84圈绒（环保纱版）",
      result: 0,
      year: 2025,
      month: 1
    },
    {
      id: 3,
      colorCode: "K23A",
      dyeDate: "2025-01-14",
      decathlonFabric: "CATS MM REC",
      deweiFabric: "JPTQ0002 50/72低弹圈绒（GRS环保）-157-PPE",
      result: 1,
      year: 2025,
      month: 1
    }
  ]
  
  rawData.value = mockData
  apiStatus.value = `模拟数据 - ${mockData.length}条`
  ElMessage.success(`已加载${mockData.length}条模拟数据`)
}

// 清空数据
const clearData = () => {
  rawData.value = []
  apiStatus.value = '已清空'
  ElMessage.info('数据已清空')
}

// 暴露方法给父组件
defineExpose({
  testAPI,
  loadMockData,
  clearData
})
</script>

<style scoped lang="scss">
.fabric-analysis-simple {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-section,
.action-section,
.data-section,
.no-data-section,
.error-section {
  margin-bottom: 20px;
}

.action-section {
  .el-button {
    margin-right: 10px;
  }
}

.error-section {
  .error-item {
    margin-bottom: 5px;
    color: #d63031;
    font-size: 14px;
  }
}
</style>
