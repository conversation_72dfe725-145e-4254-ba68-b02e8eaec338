/**
 * 快速API测试工具
 * 在浏览器控制台中使用
 */

// 快速测试API连接
window.quickTestAPI = async function() {
  console.log('=== 开始快速API测试 ===');
  
  try {
    const url = 'http://223.95.171.10:18090/api/data/invoke/1/analysis-data/0?api_secret=DRyEnruYbgYOGbiw&startDate=2025-01-01&endDate=2025-06-24';
    
    console.log('请求URL:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('响应状态:', response.status, response.statusText);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    console.log('✅ API调用成功!');
    console.log('响应数据:', data);
    console.log('数据条数:', data.data?.length || 0);
    
    if (data.data && data.data.length > 0) {
      console.log('第一条数据示例:', data.data[0]);
    }
    
    return data;
    
  } catch (error) {
    console.error('❌ API调用失败:', error);
    console.error('错误详情:', error.message);
    throw error;
  }
};

// 测试数据转换
window.testDataTransform = function(apiData) {
  if (!Array.isArray(apiData)) {
    console.error('输入数据不是数组');
    return [];
  }
  
  console.log('=== 开始数据转换测试 ===');
  console.log('原始数据条数:', apiData.length);
  
  const transformedData = apiData.map(item => ({
    id: Number(item.ID) || 0,
    colorCode: item.colorCode || '',
    dyeDate: item.dyeDate ? item.dyeDate.split(' ')[0] : '',
    decathlonFabric: item.decathlonFabric || '',
    deweiFabric: item.deweiFabric || '',
    result: item.result === "1" || item.result === 1 ? 1 : 0,
    year: Number(item.year) || new Date().getFullYear(),
    month: Number(item.month) || new Date().getMonth() + 1,
    dyeingConsistency: item.dyeingConsistency === "true" || item.dyeingConsistency === true ? 1 : 0,
    timeRFT: item.timeRFT === "true" || item.timeRFT === true ? 1 : 0,
    colorRFT: item.colorRFT === "true" || item.colorRFT === true ? 1 : 0,
    cfRFT: item.cfRFT === "true" || item.cfRFT === true ? 1 : 0,
    inspectionResult: item.inspectionResult === "true" || item.inspectionResult === true ? 1 : 0,
    mainDefect: item.mainDefect || null
  }));
  
  console.log('转换后数据条数:', transformedData.length);
  console.log('转换后第一条数据:', transformedData[0]);
  
  return transformedData;
};

// 完整测试流程
window.fullAPITest = async function() {
  try {
    console.log('🚀 开始完整API测试流程...');
    
    // 1. 测试API调用
    const apiResponse = await window.quickTestAPI();
    
    // 2. 测试数据转换
    const transformedData = window.testDataTransform(apiResponse.data);
    
    // 3. 统计分析
    console.log('=== 数据统计 ===');
    const totalCount = transformedData.length;
    const passCount = transformedData.filter(item => item.result === 1).length;
    const failCount = transformedData.filter(item => item.result === 0).length;
    const passRate = totalCount > 0 ? ((passCount / totalCount) * 100).toFixed(1) : 0;
    
    console.log(`总记录数: ${totalCount}`);
    console.log(`合格数: ${passCount}`);
    console.log(`不合格数: ${failCount}`);
    console.log(`合格率: ${passRate}%`);
    
    // 4. 色号统计
    const colorStats = {};
    transformedData.forEach(item => {
      if (item.colorCode) {
        if (!colorStats[item.colorCode]) {
          colorStats[item.colorCode] = { total: 0, pass: 0 };
        }
        colorStats[item.colorCode].total++;
        if (item.result === 1) {
          colorStats[item.colorCode].pass++;
        }
      }
    });
    
    console.log('=== TOP5色号统计 ===');
    const topColors = Object.entries(colorStats)
      .sort((a, b) => b[1].total - a[1].total)
      .slice(0, 5);
    
    topColors.forEach(([color, stats]) => {
      const rate = ((stats.pass / stats.total) * 100).toFixed(1);
      console.log(`${color}: ${stats.total}缸, 合格率${rate}%`);
    });
    
    console.log('✅ 完整测试流程完成!');
    
    return {
      apiResponse,
      transformedData,
      stats: {
        totalCount,
        passCount,
        failCount,
        passRate
      },
      topColors
    };
    
  } catch (error) {
    console.error('❌ 完整测试失败:', error);
    throw error;
  }
};

console.log('🔧 API测试工具已加载!');
console.log('使用方法:');
console.log('- window.quickTestAPI() - 快速测试API');
console.log('- window.fullAPITest() - 完整测试流程');
console.log('- window.testDataTransform(data) - 测试数据转换');
