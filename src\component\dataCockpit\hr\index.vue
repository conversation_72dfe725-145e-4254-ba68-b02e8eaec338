<template>
    <div class="center">
        <div class="up">
            <div class="bg-color-black item" v-for="item in titleItem" :key="item.title">
                <p class="ml-3 colorBlue fw-b">{{ item.title }}</p>
                <div>
                    <dv-digital-flop class="dv-dig-flop ml-1 mt-1 pl-3" :config="item.config" />
                </div>
            </div>
        </div>
        <div class="down">
            <div class="ranking bg-color-black">
                <span>
                    <i class="iconfont icon-tongji2"></i>
                </span>
                <span class="">人均产能</span>
                <dv-scroll-ranking-board class="dv-scr-rank-board" :config="ranking" />
            </div>
            <div class="percent">
                <div class="item bg-color-black">
                    <span>学历</span>
                    <slot name="rate1">
                        <!-- 默认内容，原来的chart组件 -->
                        <Echarts :option="props.options3" :height="80"></Echarts>
                    </slot>
                </div>
                <div class="item bg-color-black">
                    <span>婚育</span>
                    <slot name="rate2">
                        <!-- 默认内容，原来的chart组件 -->
                        <Echarts :option="props.options4" :height="80"></Echarts>
                    </slot>
                </div>
                <div class="water">
                    <h3 style="text-align: center;">招聘达成率</h3>
                    <dv-water-level-pond class="dv-wa-le-po" :config="water" />

                </div>
            </div>
        </div>
    </div>
</template>

<script setup name="count">
import Echarts from '@/echarts/min/index.vue'
import { reactive, onMounted, computed, watch } from 'vue'


const props = defineProps({
    options3: {
        type: Object,
        default: {}

    },
    options4: {
        type: Object,
        default: {}
    },
    titleData: {
        type: Array,
    }
})


const titleItem = reactive([])

// 初始化数据
const setData = () => {
    props.titleData.forEach(e => {
        titleItem.push({
            title: e.text,
            config: {
                number: [e.number],
                toFixed: 0,
                textAlign: 'left',
                content: '{nt}',
                style: {
                    fontSize: 26
                }
            }
        })
    })
}
const clearData = () => {
    titleItem.splice(0)
}

onMounted(() => {
    setData()
})
watch(() => props.titleData, (newVal, oldVal) => {
    clearData()
    setData()
})

const ranking = reactive({
    data: [
        { name: '项目一', value: 10 },
        { name: '项目二', value: 9 },
        { name: '项目三', value: 12 },
        { name: '项目四', value: 10 },
        { name: '项目五', value: 2 },
        { name: '项目六', value: 1 },
        { name: '项目七', value: 5 },
        { name: '项目八', value: 7 },
        { name: '项目九', value: 6 },
        { name: '项目十', value: 11 }
    ],
    carousel: 'single',
    unit: '个'
})
const rate = reactive([
    {
        id: 'centerRate1',
        tips: 60,
        colorData: {
            textStyle: '#3fc0fb',
            series: {
                color: ['#00bcd44a', 'transparent'],
                dataColor: {
                    normal: '#03a9f4',
                    shadowColor: '#97e2f5'
                }
            }
        }
    },
    {
        id: 'centerRate2',
        tips: 40,
        colorData: {
            textStyle: '#67e0e3',
            series: {
                color: ['#faf3a378', 'transparent'],
                dataColor: {
                    normal: '#ff9800',
                    shadowColor: '#fcebad'
                }
            }
        }
    }
])
const water = reactive({
    data: [50, 60],
    shape: 'roundRect',
    formatter: '97%',
    waveNum: 3
})
// const tips1 = 80
// const tips2 = 95
// const createOption = (val, num) => {
//     return {
//         title: {
//             text: val * 1 + "%",
//             x: "center",
//             y: "center",
//             textStyle: {
//                 color: rate[num].colorData.textStyle,
//                 fontSize: 16
//             }
//         },
//         series: [
//             {
//                 type: "pie",
//                 radius: ["75%", "80%"],
//                 center: ["50%", "50%"],
//                 hoverAnimation: false,
//                 color: rate[num].colorData.series.color,
//                 label: {
//                     normal: {
//                         show: false
//                     }
//                 },
//                 data: [
//                     {
//                         value: val,
//                         itemStyle: {
//                             normal: {
//                                 color: rate[num].colorData.series.dataColor.normal,
//                                 shadowBlur: 10,
//                                 shadowColor: rate[num].colorData.series.dataColor.shadowColor
//                             }
//                         }
//                     },
//                     {
//                         value: 100 - val
//                     }
//                 ]
//             }
//         ]
//     }
// }
// const options3 = computed(() => {
//     return createOption(tips1, 0)
// })
// const options4 = computed(() => {
//     return createOption(tips2, 1)
// })

</script>

<style lang="scss" scoped>
.center {
    display: flex;
    flex-direction: column;

    .up {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;

        .item {
            border-radius: 6px;
            padding-top: 8px;
            margin-top: 8px;
            width: 32%;
            height: 70px;

            .dv-dig-flop {
                width: 150px;
                height: 30px;
            }
        }
    }

    .down {
        padding: 6px 4px;
        padding-bottom: 0;
        width: 100%;
        display: flex;
        height: 255px;
        justify-content: space-between;

        .bg-color-black {
            border-radius: 5px;
        }

        .ranking {
            padding: 10px;
            width: 59%;

            .dv-scr-rank-board {
                height: 220px;
            }
        }

        .percent {
            width: 40%;
            display: flex;
            flex-wrap: wrap;

            .item {
                width: 50%;
                height: 120px;

                span {
                    margin-top: 8px;
                    font-size: 14px;
                    display: flex;
                    justify-content: center;
                }
            }

            .water {
                width: 100%;

                .dv-wa-le-po {
                    height: 120px;
                }
            }
        }
    }
}
</style>
