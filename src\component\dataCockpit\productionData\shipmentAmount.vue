<template>
    <div>
        <div class="title-style" :style="{ backgroundColor: titleColor }">
            <h3>{{ title }}</h3>
        </div>
        <table border="1" cellspacing="0" cellpadding="5">
            <thead>
                <tr>
                    <th>出货金额(单位：万元)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="font-weight: bolder;font-size: large;" class="ShipmentAmountAll">{{ data.one }}</td>
                </tr>
                <tr>
                    <td class="ShipmentAmountRz">{{ data.two }}</td>
                </tr>
                <tr>
                    <td class="ShipmentAmountTh">{{ data.three }}</td>
                </tr>
                <tr>
                    <td class="ShipmentAmountPb">{{ data.four }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';

import { DataProps } from './interface';
const props = defineProps({
    title: {
        type: String,
        required: false,
        default: '一、出货金额'
    },
    data: {
        type: Object as PropType<DataProps>,
        required: false,
        default: () => ({})
    },
    titleColor: {
        type: String,
        required: false,

    }

})

const title = props.title ?? '一、出货金额';

</script>

<style lang="scss" scoped>
@import './index.scss';
</style>