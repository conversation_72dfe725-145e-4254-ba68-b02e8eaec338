
/**
 * 记录类型定义，包含可选字段和扩展属性
 * @typedef {Object} RecordType
 * @property {string} [离职类别] - 离职类别（可选字段，允许不存在或为undefined）
 * @property {unknown} [key: string] - 其他扩展字段（允许任意类型的键值对）
 */
type RecordType = {
    离职类别?: string; // 允许字段不存在或为 undefined
    [key: string]: unknown; // 其他字段允许任意类型
};
/**
 * 将Excel日期序列号转换为JavaScript Date对象
 * @param {number} serial - Excel日期序列号（从1900-01-01开始计算的整数）
 * @returns {Date} 转换后的JavaScript日期对象（不带时间部分）
 * @description 
 * - Excel的日期系统从1900-01-01开始，而JavaScript从1970-01-01开始
 * - 修正Excel中错误存在的1900年2月29日问题（自动减去1天）
 * - 返回的结果将时间部分设置为00:00:00
 */
function excelDateToJSDate(serial: number): Date {
    // Excel的序列号从1900-01-01开始，JavaScript从1970-01-01
    // Excel中1900年2月29日是错误存在，需减去1天
    const utc_days = Math.floor(serial - 25569) // 25569是1970-01-01对应的Excel序列号
    const utc_value = utc_days * 86400 // 秒数
    const date_info = new Date(utc_value * 1000)
    return new Date(date_info.getFullYear(), date_info.getMonth(), date_info.getDate())
}
/**
 * 通用日期解析函数
 * @param {string|number|Date} value - 需要解析的日期值，支持以下格式：
 * - Excel序列号（数字类型）
 * - ISO日期字符串（如"2023-01-01"）
 * - null/undefined/空字符串返回null
 * @returns {Date|null} 解析成功的Date对象，解析失败返回null
 * @example
 * parseDate(44197)    // Excel日期 → 2021-01-01
 * parseDate("2021-01-01") // 字符串日期 → 2021-01-01
 */
function parseDate(value: String | Number | Date | any): Date | null {
    if (value === null || value === undefined || value === '') return null

    if (typeof value === 'number') {
        // 处理Excel日期序列号
        return excelDateToJSDate(value)
    }

    // 处理字符串日期
    const d: any = new Date(value)
    return isNaN(d) ? null : d
}


/**
 * 计算精确年龄（周岁）
 * @param {Date|null|undefined} birthDate - 出生日期对象
 * @param {Date} [refDate=new Date()] - 参考日期，默认为当前日期
 * @returns {number|null} 精确年龄（整数，单位：岁），输入无效时返回null
 * @description
 * - 考虑月份和日期的边界条件（未到生日减1岁）
 * - 示例：当参考日期为2023-03-20时：
 *   - 生日2000-03-21 → 22岁（未过生日）
 *   - 生日2000-03-20 → 23岁（已过生日）
 */
function calcAge(birthDate: { getFullYear: () => number; getMonth: () => number; getDate: () => number; }, refDate: Date = new Date()): number | null {
    if (!birthDate) return null
    let age = refDate.getFullYear() - birthDate.getFullYear()
    if (
        refDate.getMonth() < birthDate.getMonth() ||
        (refDate.getMonth() === birthDate.getMonth() && refDate.getDate() < birthDate.getDate())
    ) {
        age--
    }
    return age
}

/**
 * 计算精确工龄（保留两位小数）
 * @param {Date|null|undefined} entryDate - 入职日期对象
 * @param {Date} [refDate=new Date()] - 参考日期，默认为当前日期
 * @returns {number} 工龄数值（单位：年，保留两位小数），输入无效时返回0
 * @description
 * - 计算逻辑：(参考日期 - 入职日期) / 全年毫秒数
 * - 返回值示例：3.75 表示3年9个月
 * - 注意：计算结果为近似值，未考虑闰年等日历差异
 */
function calcSeniority(entryDate: any, refDate: any): number {

    if (!entryDate) return 0
    const diff = (refDate - entryDate) / (1000 * 3600 * 24 * 365)
    return diff
}

/**
 * 判断人员是否在职
 * @param {Date|string|null|undefined} leaveDate - 离职日期（支持Date对象、日期字符串或空值）
 * @returns {boolean} 在职状态
 * @description
 * - 逻辑判断规则：
 *   - 无离职日期 → 视为在职（返回true）
 *   - 离职日期大于等于当前日期 → 仍视为在职（允许设置未来离职日期）
 *   - 离职日期早于当前日期 → 已离职（返回false）
 * @example
 * isActive(null)      // true （未设置离职日期）
 * isActive("2025-01-01") // true （未来离职日期）
 * isActive("2020-01-01") // false
 */
function isActive(leaveDate: string | number | Date): boolean {
    if (!leaveDate) return true
    const leave = new Date(leaveDate)
    const now = new Date()
    return leave >= now
}

/**
 * 对象数组分组工具
 * @param {Array<Object>} array - 需要分组的对象数组
 * @param {string} key - 分组依据的对象属性名
 * @returns {Object<string, Array>} 分组结果对象
 * @description
 * - 返回对象的键为属性值，值为包含对应元素的数组
 * - 分组属性不存在时将使用"undefined"作为键
 * - 空数组输入将返回空对象
 * @example
 * groupBy([{type:'A'}, {type:'B'}], 'type') 
 * // 返回 { A: [{type:'A'}], B: [{type:'B'}] }
 */
function groupBy(array: any[], key: string) {
    return array.reduce((acc, item) => {
        const k = item[key]
        if (!acc[k]) acc[k] = []
        acc[k].push(item)
        return acc
    }, {})
}

/**
 * 分类统计工具（支持部门过滤）
 * @param {Array<RecordType>} persons - 人员数据数组
 * @param {string} field - 需要统计的字段名
 * @param {Array<string>} [filterDepartments=[]] - 需要过滤的部门列表（可选）
 * @returns {Object<string, number>} 分类统计结果（分类值: 数量） 
 * @description
 * - 统计逻辑：
 *   1. 先进行部门过滤（当filterDepartments非空时）
 *   2. 对字段值为null/undefined/空的情况归类为"未知"
 *   3. 数值型字段会被自动转为字符串作为键
 * - 特别说明：部门过滤采用严格包含判断（区分大小写）
 * @example
 * countByField(employees, 'gender', ['研发部'])
 * // 返回 { 男: 15, 女: 8, 未知: 2 }
 */
function countByField(persons: any[], field: string, filterDepartments: Array<string> = []): { [s: string]: number; } {
    const counts = {}
    persons.forEach(p => {
        if (filterDepartments.length > 0 && !filterDepartments.includes(p.department)) return
        const val = p[field] || '未知'
        counts[val] = (counts[val] || 0) + 1
    })
    return counts
}

// HR分析类
class HRAnalyzer {
    rawData: any
    groupedById: {} | {
        id: any,
        records: RecordType
    }
    persons: any[]
    constructor(rawData) {
        this.rawData = rawData
        this.groupedById = {}
        this.persons = [] // 每个人的汇总信息
        this.preprocess()
    }

    preprocess() {
        this.groupedById = groupBy(this.rawData, '身份证号码')
        const now = new Date()
        this.persons = []

        for (const [id, records] of Object.entries(this.groupedById)) {
            const isActive = records.some((r) => {
                const leaveDate = parseDate(r['离职日期'])
                return !leaveDate || leaveDate >= now
            })

            const birthDate = parseDate(records[0]['出生日期'])
            const age = calcAge(birthDate, now)

            let totalSeniority = 0
            const processedRecords = records.map((r) => {

                const entryDate = parseDate(r['进公司时间'])
                const leaveDate = parseDate(r['离职日期'])
                const seniority = calcSeniority(entryDate, now) // 这里必须是calcSeniority
                const leaveSeniority = calcSeniority(entryDate, leaveDate)
                totalSeniority += seniority
                return {
                    ...r,
                    entryDate,
                    seniority,
                    leaveDate,
                    leaveSeniority
                }
            })

            this.persons.push({
                id,
                records: processedRecords,
                isActive,
                age,
                totalSeniority,
                department: records[0]['部门'],
                marriage: records[0]['婚姻状况'] || '未知',
                education: records[0]['学历'] || '未知',
                resignTypes: [...new Set(records.map(r => r['离职类别']).filter(Boolean))],
            })
        }
    }

    // 工龄分析（按年龄区间统计所有记录的工龄人数）
    seniorityAnalysis(departments = [], seniorityRanges = [0, 1, 3, 5, 10, 25]) {
        let records = []
        let persons = this.persons.filter(p => p.isActive)
        if (departments.length > 0) {

            persons.forEach(p => {
                if (departments.includes(p.department)) {
                    for (let index = 0; index < p.records.length; index++) {
                        const element = p.records[index];
                        if (element["离职日期"]) {
                            continue
                        } else {
                            records.push(element)
                        }
                    }
                }
            })
        } else {
            persons.forEach(p => {
                for (let index = 0; index < p.records.length; index++) {
                    const element = p.records[index];
                    if (element["离职日期"]) {
                        continue
                    } else {
                        records.push(element)
                    }
                }
            })
        }
        // 生成工龄区间标签
        const labels = []
        for (let i = 0; i < seniorityRanges.length - 1; i++) {
            labels.push(`${seniorityRanges[i]}-${seniorityRanges[i + 1]}`)
        }
        labels.push(`${seniorityRanges[seniorityRanges.length - 1]}+`)

        const counts = labels.map(() => 0)

        records.forEach(r => {

            const seniority = r.seniority
            if (seniority === null || seniority === undefined) return
            for (let i = 0; i < seniorityRanges.length; i++) {
                if (i == seniorityRanges.length - 1) {
                    if (seniority >= seniorityRanges[i]) {
                        counts[i]++
                        break
                    }
                } else {
                    if (seniority >= seniorityRanges[i] && seniority < seniorityRanges[i + 1]) {
                        counts[i]++
                        break
                    }
                }
            }
        })
        const pieData = labels.map((label, index) => ({
            value: counts[index],
            name: label
        }))
        return {
            series: [{
                name: '工龄人数',
                type: 'bar',
                data: pieData,
            }],
            xAxis: { type: 'category', data: labels },
            yAxis: { type: 'value' },
            legend: { data: ['工龄人数'] },
        }
    }

    // 年龄分析（当前在职人员，按年龄区间统计人数）
    ageAnalysis(departments = [], ageRanges = [18, 25, 30, 40, 50, 60]) {
        let persons = this.persons.filter(p => p.isActive)
        if (departments.length > 0) {
            persons = persons.filter(p => departments.includes(p.department))
        }

        const ageLabels = []
        for (let i = 0; i < ageRanges.length - 1; i++) {
            ageLabels.push(`${ageRanges[i]}-${ageRanges[i + 1]}`)
        }
        ageLabels.push(`${ageRanges[ageRanges.length - 1]}+`)

        const counts = ageLabels.map(() => 0)

        persons.forEach(p => {
            const age = p.age
            if (age === null) return
            for (let i = 0; i < ageRanges.length; i++) {
                if (i === ageRanges.length - 1) {
                    if (age >= ageRanges[i]) {
                        counts[i]++
                        break
                    }
                } else {
                    if (age >= ageRanges[i] && age < ageRanges[i + 1]) {
                        counts[i]++
                        break
                    }
                }
            }
        })
        const pieData = ageLabels.map((label, index) => ({
            value: counts[index],
            name: label
        }))
        return {
            series: [{
                name: '年龄人数',
                type: 'bar',
                data: pieData,
            }],
            xAxis: { type: 'category', data: ageLabels },
            yAxis: { type: 'value' },
            legend: { data: ['年龄人数'] },
        }
    }

    // 公司人员总览
    // companyEmployees(departments = [], yearMonth = '') {
    //     const ym = yearMonth.split('-')
    //     const year = parseInt(ym[0])
    //     const month = parseInt(ym[1])

    //     let persons = this.persons
    //     if (departments.length > 0) {
    //         persons = persons.filter(p => departments.includes(p.department))
    //     }

    //     // 总人数：当前在职人数
    //     const total = persons.filter(p => p.isActive).length

    //     // 编制人数：假设职务或岗位中含“编制”关键字
    //     const headcount = persons.filter(p => p.isActive && (p.records.some(r => (r['岗位']?.includes('编制') || r['职务']?.includes('编制'))))).length

    //     // 当月入职人数（按记录统计）
    //     let entryNumberThisMonth = 0
    //     let entryNumberThisYear = 0
    //     let resignNumberThisMonth = 0
    //     let resignNumberThisYear = 0

    //     persons.forEach(p => {
    //         p.records.forEach(r => {
    //             if (!r.entryDate) return
    //             if (r.entryDate.getFullYear() === year) {
    //                 entryNumberThisYear++
    //                 if (r.entryDate.getMonth() + 1 === month) {
    //                     entryNumberThisMonth++
    //                 }
    //             }
    //             if (r.leaveDate) {
    //                 if (r.leaveDate.getFullYear() === year) {
    //                     resignNumberThisYear++
    //                     if (r.leaveDate.getMonth() + 1 === month) {
    //                         resignNumberThisMonth++
    //                     }
    //                 }
    //             }
    //         })
    //     })

    //     return {
    //         total,
    //         headcount,
    //         entryNumberThisMonth,
    //         entryNumberThisYear,
    //         resignNumberThisMonth,
    //         resignNumberThisYear,
    //     }
    // }
    companyEmployees(departments = [], yearMonth = '', headcountData = []) {
        const ym = yearMonth.split('-')
        const year = parseInt(ym[0])
        const month = parseInt(ym[1])

        let persons = this.persons
        if (departments.length > 0) {
            persons = persons.filter(p => departments.includes(p.department))
        }

        // 总人数：当前在职人数
        const total = persons.filter(p => p.isActive).length

        // 编制人数：从headcountData中根据departments匹配中心字段累加
        let headcount = 0
        if (departments.length > 0) {
            headcount = headcountData
                .filter(item => departments.includes(item.中心))
                .reduce((sum, item) => sum + item.编制人数, 0)
        } else {
            headcount = headcountData.reduce((sum, item) => sum + item.编制人数, 0)
        }

        // 当月入职人数（按记录统计）
        let entryNumberThisMonth = 0
        let entryNumberThisYear = 0
        let resignNumberThisMonth = 0
        let resignNumberThisYear = 0

        persons.forEach(p => {
            p.records.forEach(r => {
                if (!r.entryDate) return
                if (r.entryDate.getFullYear() === year) {
                    entryNumberThisYear++
                    if (r.entryDate.getMonth() + 1 === month) {
                        entryNumberThisMonth++
                    }
                }
                if (r.leaveDate) {
                    if (r.leaveDate.getFullYear() === year) {
                        resignNumberThisYear++
                        if (r.leaveDate.getMonth() + 1 === month) {
                            resignNumberThisMonth++
                        }
                    }
                }
            })
        })

        return {
            total,
            headcount,
            entryNumberThisMonth,
            entryNumberThisYear,
            resignNumberThisMonth,
            resignNumberThisYear,
        }
    }


    // 学历分析（当前在职人员，按学历分类统计）
    educationAnalysis(departments = []) {
        let persons = this.persons.filter(p => p.isActive)
        if (departments.length > 0) {
            persons = persons.filter(p => departments.includes(p.department))
        }

        const counts = countByField(persons, 'education')

        const categories = Object.keys(counts)
        const data = Object.entries(counts).map(([name, value]) => ({ name, value }))

        return {
            series: [{
                name: '学历人数',
                type: 'pie',
                data,
            }],
            xAxis: { type: 'category', data: categories },
            yAxis: { type: 'value' },
            legend: { data: ['学历人数'] },
        }
    }

    // 婚姻分析（当前在职人员，按婚姻状况分类统计）
    marriageAnalysis(departments = []) {
        let persons = this.persons.filter(p => p.isActive)
        if (departments.length > 0) {
            persons = persons.filter(p => departments.includes(p.department))
        }

        const counts = countByField(persons, 'marriage')

        const categories = Object.keys(counts)
        const data = Object.entries(counts).map(([name, value]) => ({ name, value }))

        return {
            series: [{
                name: '婚姻人数',
                type: 'pie',
                data,
            }],
            xAxis: { type: 'category', data: categories },
            yAxis: { type: 'value' },
            legend: { data: ['婚姻人数'] },
        }
    }

    // 离职工龄分析（离职记录，按年龄区间统计）
    resignSeniorityAnalysis(departments = [], seniorityRanges = [0, 1, 3, 5, 10, 25]) {
        //leaveSeniority
        let records = []
        let persons = this.persons.filter(p => {
            return true
        })
        if (departments.length > 0) {
            persons.forEach(p => {
                if (departments.includes(p.department)) {
                    for (let index = 0; index < p.records.length; index++) {
                        const element = p.records[index];
                        if (element["离职日期"]) {
                            records.push(element)
                        }
                    }
                }
            })
        } else {
            persons.forEach(p => {
                for (let index = 0; index < p.records.length; index++) {
                    const element = p.records[index];
                    if (element["离职日期"]) {
                        records.push(element)
                    }
                }
            })
        }
        // 生成工龄区间标签
        const labels = []
        for (let i = 0; i < seniorityRanges.length - 1; i++) {
            labels.push(`${seniorityRanges[i]}-${seniorityRanges[i + 1]}`)
        }
        labels.push(`${seniorityRanges[seniorityRanges.length - 1]}+`)
        const counts = labels.map(() => 0)

        records.forEach(r => {

            const seniority = r.leaveSeniority
            if (seniority === null || seniority === undefined) return
            for (let i = 0; i < seniorityRanges.length; i++) {
                if (i == seniorityRanges.length - 1) {
                    if (seniority >= seniorityRanges[i]) {
                        counts[i]++
                        break
                    }
                } else {
                    if (seniority >= seniorityRanges[i] && seniority < seniorityRanges[i + 1]) {
                        counts[i]++
                        break
                    }
                }
            }
        })
        const pieData = labels.map((label, index) => ({
            value: counts[index],
            name: label
        }))
        return {
            series: [{
                name: '工龄人数',
                type: 'bar',
                data: pieData,
            }],
            xAxis: { type: 'category', data: labels },
            yAxis: { type: 'value' },
            legend: { data: ['工龄人数'] },
        }
    }

    // 离职类别分析（离职人员，按离职类别统计）
    resignTypeAnalysis(departments = []) {


        let persons = this.persons.filter(p => {
            // 只要有离职记录即视为离职人员
            return p.records.some(r => r.leaveDate && r.leaveDate < new Date())
        })
        if (departments.length > 0) {
            persons = persons.filter(p => departments.includes(p.department))
        }

        // 统计离职类别，可能一个人多个类别，计数时按类别统计所有出现次数
        const counts = {}
        persons.forEach(p => {
            p.records.forEach(r => {
                if (r.leaveDate && r.leaveDate < new Date()) {
                    const type = r['离职类别'] || '未知'
                    counts[type] = (counts[type] || 0) + 1
                }
            })
        })

        const categories = Object.keys(counts)
        const data = categories.map(c => counts[c])

        return {
            series: [{
                name: '离职类别人数',
                type: 'bar',
                data,
            }],
            xAxis: { type: 'category', data: categories },
            yAxis: { type: 'value' },
            legend: { data: ['离职类别人数'] },
        }
    }

    // 离职率分析
    // 返回两个series，一个按时间（月）维度，一个按部门维度
    resignRateAnalysis(departments = [], date) {
        const now = new Date(date);
        const startYear = now.getFullYear();

        // 构造最近13个月的月份数组，格式 'YYYY-MM'
        const months = [];
        for (let i = 0; i <= 12; i++) {
            const d = new Date(startYear, now.getMonth() - 12 + i, 1);
            const y = d.getFullYear();
            const m = d.getMonth() + 1;
            months.push(`${y}-${m.toString().padStart(2, '0')}`);
        }

        // 计算每个月的最后一天，用于判断在职状态
        const monthEndDates = months.map(m => {
            const [y, mon] = m.split('-').map(Number);
            return new Date(y, mon, 0); // 当月最后一天
        });

        // 初始化统计对象
        const entryCountByMonth = {};
        const resignCountByMonth = {};
        const inServiceCountByMonth = {}; // 旧版在职人数，暂时保留
        const firstDayInServiceCountByMonth = {};
        const lastDayInServiceCountByMonth = {};

        months.forEach(m => {
            entryCountByMonth[m] = 0;
            resignCountByMonth[m] = 0;
            inServiceCountByMonth[m] = 0;
            firstDayInServiceCountByMonth[m] = 0;
            lastDayInServiceCountByMonth[m] = 0;
        });

        // 获取所有部门（过滤条件下）
        const deptSet = new Set();
        this.persons.forEach(p => {
            if (departments.length === 0 || departments.includes(p.department)) {
                deptSet.add(p.department);
            }
        });
        const depts = Array.from(deptSet);

        // 初始化部门统计
        const entryCountByDept = {};
        const resignCountByDept = {};
        const inServiceCountByDept = {};

        depts.forEach((d: any) => {
            entryCountByDept[d] = 0;
            resignCountByDept[d] = 0;
            inServiceCountByDept[d] = 0;
        });

        // 构造年份数组，往前推3年，共4年
        const years = [];
        for (let y = startYear - 3; y <= startYear; y++) {
            years.push(y);
        }

        // 初始化年度统计对象
        const entryCountByYear = {};
        const resignCountByYear = {};
        const firstDayInServiceCountByYear = {};
        const lastDayInServiceCountByYear = {};

        years.forEach(y => {
            entryCountByYear[y] = 0;
            resignCountByYear[y] = 0;
            firstDayInServiceCountByYear[y] = 0;
            lastDayInServiceCountByYear[y] = 0;
        });

        // 统计数据
        this.persons.forEach(p => {
            if (departments.length > 0 && !departments.includes(p.department)) return;

            p.records.forEach(r => {
                // 时间维度统计
                months.forEach((m, idx) => {
                    const monthEnd = monthEndDates[idx];
                    const [y, mon] = m.split('-').map(Number);
                    const monthFirstDay = new Date(y, mon - 1, 1);

                    // 入职人数（入职日期在当月）
                    if (r.entryDate) {
                        const entryYM = `${r.entryDate.getFullYear()}-${(r.entryDate.getMonth() + 1).toString().padStart(2, '0')}`;
                        if (entryYM === m) {
                            entryCountByMonth[m]++;
                        }
                    }

                    // 离职人数（离职日期在当月）
                    if (r.leaveDate) {
                        const leaveYM = `${r.leaveDate.getFullYear()}-${(r.leaveDate.getMonth() + 1).toString().padStart(2, '0')}`;
                        if (leaveYM === m) {
                            resignCountByMonth[m]++;
                        }
                    }

                    // 旧版在职人数（入职日期 <= 当月最后一天 且 (无离职日期 或 离职日期 > 当月最后一天)）
                    if (r.entryDate && r.entryDate <= monthEnd && (!r.leaveDate || r.leaveDate > monthEnd)) {
                        inServiceCountByMonth[m]++;
                    }

                    // 新算法：每月第一天在职人数
                    if (r.entryDate && r.entryDate <= monthFirstDay && (!r.leaveDate || r.leaveDate > monthFirstDay)) {
                        firstDayInServiceCountByMonth[m]++;
                    }
                    // 新算法：每月最后一天在职人数
                    if (r.entryDate && r.entryDate <= monthEnd && (!r.leaveDate || r.leaveDate > monthEnd)) {
                        lastDayInServiceCountByMonth[m]++;
                    }
                });

                // 部门维度统计（只统计一次，避免重复计数）
                if (r.entryDate) {
                    entryCountByDept[p.department]++;
                }
                if (r.leaveDate && r.leaveDate < now) {
                    resignCountByDept[p.department]++;
                }
                if (r.entryDate && r.entryDate <= now && (!r.leaveDate || r.leaveDate > now)) {
                    inServiceCountByDept[p.department]++;
                }

                // 年度维度统计
                years.forEach(y => {
                    const yearFirstDay = new Date(y, 0, 1);
                    const yearLastDay = new Date(y, 11, 31);

                    // 入职人数（入职日期在当年）
                    if (r.entryDate && r.entryDate.getFullYear() === y) {
                        entryCountByYear[y]++;
                    }

                    // 离职人数（离职日期在当年）
                    if (r.leaveDate && r.leaveDate.getFullYear() === y) {
                        resignCountByYear[y]++;
                    }

                    // 年初在职人数
                    if (r.entryDate && r.entryDate <= yearFirstDay && (!r.leaveDate || r.leaveDate > yearFirstDay)) {
                        firstDayInServiceCountByYear[y]++;
                    }

                    // 年末在职人数
                    if (r.entryDate && r.entryDate <= yearLastDay && (!r.leaveDate || r.leaveDate > yearLastDay)) {
                        lastDayInServiceCountByYear[y]++;
                    }
                });
            });
        });

        // 计算当月离职率（新算法）
        const resignRateByMonth = months.map(m => {
            const resign = resignCountByMonth[m];
            const firstDayCount = firstDayInServiceCountByMonth[m];
            const lastDayCount = lastDayInServiceCountByMonth[m];
            const avgInService = (firstDayCount + lastDayCount) / 2;
            return avgInService === 0 ? 0 : parseFloat((resign / avgInService).toFixed(4));
        });

        // 计算部门离职率（保持原逻辑）
        const resignRateByDept = depts.map((d: any) => {
            const resign = resignCountByDept[d];
            const inService = inServiceCountByDept[d];
            return inService === 0 ? 0 : parseFloat((resign / inService).toFixed(4));
        });

        // 构造年度维度图表数据
        const yearLabels = years.map(y => y.toString());

        const entryCountByYearArr = years.map(y => entryCountByYear[y]);
        const resignCountByYearArr = years.map(y => resignCountByYear[y]);
        const resignRateByYearArr = years.map(y => {
            const firstDayCount = firstDayInServiceCountByYear[y];
            const lastDayCount = lastDayInServiceCountByYear[y];
            const avgInService = (firstDayCount + lastDayCount) / 2;
            if (avgInService === 0) return 0;
            return parseFloat((resignCountByYear[y] / avgInService).toFixed(4)) * 100;
        });

        // 返回结果
        return {
            timeDimension: {
                xAxis: { type: 'category', data: months },
                yAxis: [
                    { type: 'value', name: '人数' },
                    { type: 'value', name: '离职率', min: 0, max: 1, axisLabel: { formatter: '{value}%' } }
                ],
                legend: { data: ['入职人数', '离职人数', '离职率'] },
                series: [
                    { name: '入职人数', type: 'bar', data: months.map(m => entryCountByMonth[m]) },
                    { name: '离职人数', type: 'bar', data: months.map(m => resignCountByMonth[m]) },
                    { name: '离职率', type: 'line', yAxisIndex: 1, data: resignRateByMonth.map((r: any) => (r * 100).toFixed(2)) },
                ],
            },
            deptDimension: {
                xAxis: { type: 'category', data: depts },
                yAxis: [
                    { type: 'value', name: '人数' },
                    { type: 'value', name: '离职率', min: 0, max: 1, axisLabel: { formatter: '{value}%' } }
                ],
                legend: { data: ['入职人数', '离职人数', '离职率'] },
                series: [
                    { name: '入职人数', type: 'bar', data: depts.map((d: any) => entryCountByDept[d]) },
                    { name: '离职人数', type: 'bar', data: depts.map((d: any) => resignCountByDept[d]) },
                    { name: '离职率', type: 'line', yAxisIndex: 1, data: resignRateByDept.map(r => r * 100) },
                ],
            },
            yearDimension: {
                xAxis: { type: 'category', data: yearLabels },
                yAxis: [
                    { type: 'value', name: '人数' },
                    { type: 'value', name: '离职率', min: 0, max: 1, axisLabel: { formatter: '{value}%' } }
                ],
                legend: { data: ['入职人数', '离职人数', '离职率'] },
                series: [
                    { name: '入职人数', type: 'bar', data: entryCountByYearArr },
                    { name: '离职人数', type: 'bar', data: resignCountByYearArr },
                    { name: '离职率', type: 'line', yAxisIndex: 1, data: resignRateByYearArr },
                ],
            }
        };
    }



    // 招聘达成率方法先空着
    recruitmentProgress() {
        return null
    }
}

const formattedResult = (analysisResult) => {
    if (!analysisResult.value) return ''
    return JSON.stringify(analysisResult.value, null, 2)
}
export {
    formattedResult, HRAnalyzer
}