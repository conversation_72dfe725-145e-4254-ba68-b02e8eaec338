<template>
    <!-- 新增选项卡 -->
    <el-tabs v-model="activeTab" class="demo-tabs">
        <el-tab-pane label="发票上传" name="upload">
            <div class="upload-container">
                <label class="custom-file-upload">
                    <input type="file" ref="fileInput" @change="handleFileChange" accept=".jpg,.jpeg,.png,.pdf" multiple />
                    选择文件
                </label>
                <el-button type="primary" @click="handleUpload" :disabled="!selectedFiles.length">发送</el-button>
                <el-button type="danger" @click="clearFiles" v-if="selectedFiles.length">取消选择</el-button>
            </div>
            <div v-if="selectedFiles.length" class="preview-container">
                <el-table :data="tableData" style="width: 100%">
                    <el-table-column prop="preview" label="" width="150">
                        <template #default="scope">
                            <div v-if="scope.row.isImage">
                                <img :src="scope.row.fileUrl" alt="预览图" class="image-preview"
                                    @click="openInNewTab(scope.row.fileUrl)" />
                            </div>
                            <div v-else-if="scope.row.isPdf">
                                <p @click="openInNewTab(scope.row.fileUrl)"
                                    style="cursor: pointer; color: blue; text-decoration: underline;">PDF文件</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="文件名" width="200">
                        <template #default="scope">
                            <p @click="openInNewTab(scope.row.fileUrl)"
                                style="cursor: pointer; color: blue; text-decoration: underline;">{{ scope.row.name }}</p>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="上传状态" />
                </el-table>
            </div>




        </el-tab-pane>

        <el-tab-pane label="历史发票" name="history">
        <div class="search-container">
            <el-form :model="searchForm" label-width="80px">
                <el-row :gutter="20">
                    <!-- 原有搜索条件保持不变 -->
                    <el-col :span="8">
                        <el-form-item label="发票号码">
                            <el-input v-model="searchForm.InvoiceNum" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="销售方">
                            <el-input v-model="searchForm.SellerName" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="合计金额">
                            <el-input-number v-model="searchForm.TotalAmount" :precision="2" />
                        </el-form-item>
                    </el-col>

                    <!-- 日期范围搜索区域 -->
                    <el-col :span="12">
                        <!-- 原有上传时间 -->
                        <el-form-item label="上传时间">
                            <el-date-picker
                                v-model="startEndDate"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            />
                        </el-form-item>
                        
                        <!-- 新增开票日期 -->
                        <el-form-item label="开票日期">
                            <el-date-picker
                                v-model="invoiceDateRange"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12" class="button-group">
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>

            <div class="scroll-container"> <!-- 新增滚动容器 -->
            <!-- 新增历史发票表格 -->
                <el-table :data="historyData" style="width: 100%" border>
                    <el-table-column prop="InvoiceNum" label="发票号码" width="200" />
                    <el-table-column prop="SellerName" label="销售方名称" width="240" />
                    <el-table-column prop="PurchaserName" label="购方名称" width="210" />
                    <el-table-column prop="TotalAmount" label="合计金额" width="100" />
                    <el-table-column prop="TotalTax" label="合计税额" width="100" />
                    <el-table-column prop="AmountInFiguers" label="价税合计小写" width="120" />
                    <el-table-column prop="AmountInWords" label="价税合计大写" width="220" />
                    <el-table-column prop="InvoiceDate" label="开票日期" width="130" />
                    <el-table-column prop="InvoiceType" label="发票种类" width="150" />
                    <el-table-column prop="CreateBy" label="开票人" width="80" />
                    <el-table-column prop="CreateOn" label="上传时间" width="180" />

                </el-table>
                <!-- 新增分页 -->
                <el-pagination
                    class="pagination-fixed" 
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 30, 40]"
                    :small="false"
                    :background="true"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handlePageChange"
                />
            </div>
        </el-tab-pane>
    </el-tabs>
</template>



<script setup>
import { InvoiceList, uploadInvoice } from '@/api/vatInvoice/index'; // 引入封装的上传接口
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref, watch } from 'vue';
const fileInput = ref(null); // 用于引用文件输入框
const selectedFiles = ref([]); // 用于存储选择的文件
const fileUrls = ref([]); // 用于存储文件预览的 URL
const uploadStatus = ref([]); // 用于存储每个文件的上传状态
const invoiceDateRange = ref([]);// 新增开票日期范围响应式变量

// 响应式搜索表单
const searchForm = ref({
    InvoiceNum: '',
    SellerName: '',
    TotalAmount: null,
    StartDate: '',
    EndDate: '',
    InvoiceStartDate: '',
    InvoiceEndDate: ''
})


/**
 * 日期范围选择器绑定值
 * @type {import('vue').Ref<[string, string]>}
 */
const startEndDate = ref([])


// 监听日期范围变化，拆分到开始/结束日期
watch(startEndDate, (newVal) => {
  searchForm.value.StartDate = newVal?.[0] || ''
  searchForm.value.EndDate = newVal?.[1] || ''
})

// 监听开票日期变化
watch(invoiceDateRange, (newVal) => {
    searchForm.value.InvoiceStartDate = newVal?.[0] || ''
    searchForm.value.InvoiceEndDate = newVal?.[1] || ''
})


/**
 * 处理搜索操作
 * @function
 * @async
 * @returns {Promise<void>}
 */
const handleSearch = async () => {
  currentPage.value = 1 // 重置到第一页
  await fetchHistory()
}

/**
 * 重置搜索条件
 * @function
 */
const resetSearch = () => {
  searchForm.value = {
    InvoiceNum: '',
    SellerName: '',
    TotalAmount: null,
    StartDate: '',
    EndDate: '',
    InvoiceStartDate: '',
    InvoiceEndDate: ''
  }
    startEndDate.value = []
    invoiceDateRange.value = []
    currentPage.value = 1
    fetchHistory()
}

const handleFileChange = async (event) => {
    const files = Array.from(event.target.files); // 获取选择的文件
    const validFiles = [];

    for (const file of files) {
        const isImageFile = file.type === 'image/jpeg' || file.type === 'image/png';
        const isPdfFile = file.type === 'application/pdf';

        if (!isImageFile && !isPdfFile) {
            ElMessage.error(`文件 ${file.name} 上传格式不正确!`);
            continue;
        }

        if (isImageFile) {
            const compressedFile = await compressImage(file);
            if (compressedFile.size / 1024 / 1024 >= 4) {
                ElMessage.error(`文件 ${file.name} 压缩后仍然大于 4MB!`);
                continue;
            }
            validFiles.push(compressedFile);
        } else {
            validFiles.push(file);
        }
    }

    selectedFiles.value = validFiles; // 设置选择的文件
    fileUrls.value = validFiles.map(file => URL.createObjectURL(file)); // 生成文件预览 URL
    uploadStatus.value = validFiles.map(() => '未上传'); // 初始化上传状态
};

// 压缩图片的函数
const compressImage = (file) => {
    return new Promise((resolve) => {
        const img = new Image();
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
            img.src = e.target.result;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const MAX_WIDTH = 800; // 最大宽度
                const MAX_HEIGHT = 800; // 最大高度
                let width = img.width;
                let height = img.height;

                // 计算新的宽高
                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }

                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob((blob) => {
                    resolve(new File([blob], file.name, { type: file.type }));
                }, file.type, 0.7); // 0.7 为压缩质量
            };
        };
    });
};

const handleUpload = async () => {
    if (!selectedFiles.value.length) {
        ElMessage.warning('请先选择文件!');
        return;
    }

    for (let i = 0; i < selectedFiles.value.length; i++) {
        const file = selectedFiles.value[i];
        uploadStatus.value[i] = '上传中...'; // 更新状态为上传中
        const response = await uploadInvoice(file);
        try {
            // 调用封装的上传接口
            if (response.data.success == false) {
                throw new Error("无法识别")
            }
            uploadStatus.value[i] = response.data.msg; // 更新状态为上传成功
            ElMessage.success(`${file.name} 上传成功!`);
        } catch (error) {
            uploadStatus.value[i] = response.data.msg; // 更新状态为上传失败
            ElMessage.error(`${file.name} ${response.data.msg}`);
        }
    }
};

const clearFiles = () => {
    selectedFiles.value = []; // 清空选择的文件
    fileUrls.value = []; // 清空预览 URL
    uploadStatus.value = []; // 清空上传状态
    fileInput.value.value = null; // 清空输入框的文件
};

// 判断文件是否为图片
const isImage = (file) => {
    return file.type === 'image/jpeg' || file.type === 'image/png';
};

// 判断文件是否为 PDF
const isPdf = (file) => {
    return file.type === 'application/pdf';
};

// 计算表格数据
const tableData = computed(() => {
    return selectedFiles.value.map((file, index) => ({
        fileUrl: fileUrls.value[index],
        name: file.name,
        status: uploadStatus.value[index],
        isImage: isImage(file),
        isPdf: isPdf(file),
    }));
});

// 打开新标签页
const openInNewTab = (url) => {
    window.open(url, '_blank'); // 在新标签页中打开文件
};





const activeTab = ref('upload');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const historyData = ref([]);

/**
 * 获取历史发票数据
 * @async
 * @function
 * @returns {Promise<void>}
 */
const fetchHistory = async () => {
  try {
    const res = await InvoiceList(
        currentPage.value,
        pageSize.value,
        {
        InvoiceNum: searchForm.value.InvoiceNum,
        SellerName: searchForm.value.SellerName,
        TotalAmount: searchForm.value.TotalAmount,
        StartDate: searchForm.value.StartDate,
        EndDate: searchForm.value.EndDate,
        InvoiceStartDate: searchForm.value.InvoiceStartDate,
        InvoiceEndDate: searchForm.value.InvoiceEndDate
      }
    )
        if (res.data.success) {
            historyData.value = res.data.response.InvoiceList;
            total.value = res.data.response.total;
        }
    } catch (error) {
        ElMessage.error('获取历史数据失败');
    }
};



// 修改分页处理方法携带搜索条件
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchHistory() // 自动携带搜索条件
}

const handlePageChange = (newPage) => {
  currentPage.value = newPage
  fetchHistory() // 自动携带搜索条件
}



/** 组件挂载时自动获取第一页历史数据 */
onMounted(() => {
    fetchHistory();
});




// 监听 selectedFiles 的变化，清理 URL 对象
watch(selectedFiles, (newFiles) => {
    newFiles.forEach((file, index) => {
        if (!file) {
            URL.revokeObjectURL(fileUrls[index]); // 释放 URL 对象
        }
    });
});
</script>

<style scoped>
.upload-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 20px;
}

.custom-file-upload {
    display: inline-block;
    padding: 10px 20px;
    cursor: pointer;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f5f7fa;
    color: #409eff;
    transition: background-color 0.3s;
}

.custom-file-upload:hover {
    background-color: #e6f7ff;
}

.preview-container {
    margin-top: 20px;
    text-align: center;
}

.image-preview {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    /* 添加光标样式 */
}

h3 {
    margin-bottom: 10px;
}

.el-table {
    width: 100%;
}

.demo-tabs {
    padding: 20px;
}
.el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
}

.scroll-container {
    scroll-behavior: smooth;
    max-height: calc(100vh - 180px); /* 根据实际布局调整 */
    overflow-y: auto; /* 启用垂直滚动 */
    position: relative;
    padding-bottom: 10px; /* 为分页预留空间 */
}

.pagination-fixed {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 16px 0;
    z-index: 1;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1); /* 添加顶部阴影 */
}


.search-container {
  padding: 20px;
  background: #f5f7fa;
  margin-bottom: 20px;
  border-radius: 4px;
}
.button-group {
  display: flex;
  align-items: center;
  padding-top: 5px;
}
.el-form-item {
  margin-bottom: 0;
}

</style>
