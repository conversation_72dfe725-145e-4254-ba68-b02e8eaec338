<template>
    <div id="main" ref="main" :style="{ height: height + 'px' }"></div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts/core';
// 引入柱状图图表，图表后缀都为 Chart
import { BarChart, PieChart } from 'echarts/charts';
// 引入提示框，标题，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent, ToolboxComponent, LegendComponent
} from 'echarts/components';
// 标签自动布局、全局过渡动画等特性
import { LabelLayout, UniversalTransition } from 'echarts/features';
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers';
import { ref, onMounted, onUnmounted, watch } from 'vue';
// // 注册必须的组件
echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent,
    BarChart,
    PieChart,
    LabelLayout,
    UniversalTransition,
    CanvasRenderer, ToolboxComponent, LegendComponent
]);
let main = ref()
let props = defineProps({
    option: { type: Object, default: {} },
    height: { type: Number, default: 300 }

})
let myChart: any
// 接下来的使用就跟之前一样，初始化图表，设置配置项
onMounted(() => {
    myChart = echarts.init(main.value);
    myChart.setOption(
        props.option
    );
})
onUnmounted(() => {
    myChart.dispose()
    myChart = null
})
watch(() => props.option, (newOption) => {
    if (myChart) {
        myChart.dispose()
        myChart = null
        myChart = echarts.init(main.value);
        myChart.setOption(
            props.option
        );
    }
}, { deep: true });
</script>
<style scoped lang="scss">
#main {
    width: 100%;
    overflow: hidden;
}
</style>