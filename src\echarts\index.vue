<template>
    <br>
    <div class="topFlex">
        <div>
            <slot name="icon"></slot>
        </div>
        <div class="titleFlex">
            <slot name="title">

            </slot>
            <slot name="style"> </slot>

        </div>
    </div>
    <div id="main" ref="main" :style="{ height: height + 'px', width: width + 'px' }"></div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts/core';
// 引入柱状图图表，图表后缀都为 Chart
import { BarChart, PieChart, RadarChart, LineChart } from 'echarts/charts';
// 引入提示框，标题，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent, ToolboxComponent, LegendComponent
} from 'echarts/components';

// 标签自动布局、全局过渡动画等特性
import { LabelLayout, UniversalTransition } from 'echarts/features';
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers';
import { ref, onMounted, onUnmounted, watch } from 'vue';
// // 注册必须的组件
echarts.use([
    LineChart,
    RadarChart,
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent,
    BarChart,
    PieChart,
    LabelLayout,
    UniversalTransition,
    CanvasRenderer, ToolboxComponent, LegendComponent
]);
let main = ref()
let props = defineProps({
    option: { type: Object, default: {} },
    height: { type: Number, default: 300 },
    width: { type: Number, default: 300 }

})
let myChart: any
// 接下来的使用就跟之前一样，初始化图表，设置配置项
onMounted(() => {
    myChart = echarts.init(main.value);
    myChart.setOption(
        props.option
    );
})
onUnmounted(() => {
    myChart.dispose()
    myChart = null
})
watch(() => props.option, (newOption) => {
    if (myChart) {
        myChart.dispose()
        myChart = null
        myChart = echarts.init(main.value);
        myChart.setOption(
            props.option
        );
    }
}, { deep: true });
</script>
<style scoped lang="scss">
#main {
    width: inherit;
    overflow: hidden;
}

.topFlex,
.titleFlex {
    display: flex;
    flex-wrap: nowrap;
}

.topFlex>div:first-child {
    width: 10px;

    margin: 10px;
}

.topFlex>div:last-child {
    width: 200px;
    margin: 10px;
}

::v-deep(.title) {
    flex: 0 0 80px;
    margin: 0px;
    height: 20px;
}

::v-deep(.style) {
    width: 80px;
    margin: 0px;
    height: 20px;
}
</style>