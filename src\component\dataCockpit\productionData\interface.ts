interface DataPropsStand {
    RzDayOrder: number;
    RzMonthOrder: number;
    RzIHoding: number;
    RzIIHoding: number;
    RzHoding: number;
    RzDayIProcution: number;
    RzDayIIProcution: number;
    RzDayProcution: number;
    RzMonthIProcution: number;
    RzMonthIIProcution: number;
    RzMonthProcution: number;
    RzDayShipment: number;
    RzMonthShipment: number;
    ThDayOrder: number;
    ThMonthOrder: number;
    ThIHoding: number;
    ThIIHoding: number;
    ThHoding: number;
    ThDayIProcution: number;
    ThDayIIProcution: number;
    ThDayProcution: number;
    ThMonthIProcution: number;
    ThMonthIIProcution: number;
    ThMonthProcution: number;
    ThDayShipment: number;
    ThMonthShipment: number;
    Th402LNeed: number;
    Th403LNeed: number;
}

interface DataProps {
    one: number;
    two: number;
    three: number;
    four: number;
    five: number;
    six: number;
}

export { DataProps, DataPropsStand }