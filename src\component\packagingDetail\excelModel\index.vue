<template>
    <div>
        <el-button type="primary" @click="downloadTemplate">下载 Excel 模板</el-button>
    </div>
</template>

<script setup lang="ts">
import * as XLSX from 'xlsx';

/**
 * 下载 Excel 模板
 */
const downloadTemplate = () => {
    // 定义列名
    const columns = [
        { 工卡号: '' },
        { 匹号: '' },
        { 公斤米长: '' },
        { 重量: '' },
        { 等级: '' },
    ];

    // 将列名转换为工作表
    const worksheet = XLSX.utils.json_to_sheet(columns, { header: ['工卡号', '匹号', '公斤米长', '重量', '等级'] });
    // 创建一个新的工作簿
    const workbook = XLSX.utils.book_new();
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '模板');

    // 导出 Excel 模板文件
    XLSX.writeFile(workbook, 'Excel模板.xlsx');
};
</script>

<style scoped>
/* 可以根据需要添加样式 */
</style>
