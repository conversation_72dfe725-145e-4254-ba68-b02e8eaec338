<template>
    <el-container class="login-container">
        <el-main>
            <el-form :model="form" ref="formRef" label-width="100px" class="login-form">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="form.username" placeholder="请输入工号" class="input-field" />
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input type="password" v-model="form.password" placeholder="请输入密码" class="input-field" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleLogin" class="login-button">登录</el-button>
                </el-form-item>
            </el-form>
        </el-main>
    </el-container>
</template>

<script setup lang="ts" name="login">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElForm } from 'element-plus';
import { login } from '@/api/auth';
import { formatTime } from '@/utils';
import { ElMessage } from 'element-plus';

const router = useRouter();
const formRef = ref<InstanceType<typeof ElForm> | null>(null);
const form = ref({
    username: localStorage.getItem("username"),
    password: localStorage.getItem("password"),
});

const handleLogin = () => {
    login(form.value.username, form.value.password).then((val) => {
        ElMessage.success("登录成功！");
        let token = {
            time: formatTime(Date.now(), "yyyy-MM-dd HH:mm:ss"),
            val: `${val.data.response.token_type} ${val.data.response.token}`
        };
        localStorage.setItem('token', token.val);
        localStorage.setItem("tokenTime", token.time);
        localStorage.setItem("username", form.value.username);
        localStorage.setItem("password", form.value.password);
        const fromRoute = localStorage.getItem('router') || 'my';
        setTimeout(() => {
            if (fromRoute == 'login') {
                router.push({ name: '' });
            } else {
                router.push({ name: fromRoute });
            }
        }, 100);
    }).catch((val) => {
        alert(val);
    });
};

const goHome = () => {
    router.push({ name: 'moblieIndex' });
};
</script>

<style scoped>
.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px;
    background: linear-gradient(to right, #e6edf4, #96f0f5);
    height: 100vh;
}

.login-title {
    color: #f5ca1d;
    font-size: 36px;
    margin-bottom: 20px;
}

.login-form {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.input-field {
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.login-button {
    background-color: #409eff;
    border-color: #409eff;
    transition: background-color 0.3s, border-color 0.3s;
}

.login-button:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
}

.back-button {
    margin-left: 10px;
    color: #409eff;
    transition: color 0.3s;
}

.back-button:hover {
    color: #66b1ff;
}
</style>
