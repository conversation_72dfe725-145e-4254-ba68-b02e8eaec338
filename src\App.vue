<template>
    <div class="app">
        <router-view />
    </div>
</template>
<script setup lang="ts">
</script>
<style lang="scss">
.app {
    width: 100vw;
    height: 100vh;
    background-color: rgb(255, 255, 255);
    overflow-y: hidden;
}

.el-form-item__label {
    color: rgb(0, 0, 0) !important;
}

.form-item>.el-form-item__label {
    color: rgb(0, 0, 0) !important;
}

.custom-loading .el-loading-text {
    color: #fff;
    font-weight: 600;
    font-size: 18px;
    margin-top: 10px;
}

.custom-loading .el-loading-spinner {
    font-size: 50px;
    color: #409EFF;
    /* Element Plus 主色调 */
}

.custom-loading .el-loading-mask {
    border-radius: 12px;
    box-shadow: 0 0 15px rgba(64, 158, 255, 0.6);
}
</style>
