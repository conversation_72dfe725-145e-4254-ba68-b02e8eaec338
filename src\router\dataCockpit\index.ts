import { RouteRecordRaw } from "vue-router";

const dataCockpitRoutes: Array<RouteRecordRaw> = [
    {
        path: "/hr1",
        name: "hr1",
        component: () => import("@/mobileView/product/dataCockpit/detail/hr/index.vue"),
    },
    {
        path: "/dataAnalysis",  // 新增数据分析看板路由
        name: "dataAnalysis",
        component: () => import("@/mobileView/product/dataCockpit/detail/dataAnalysis/index.vue"),
    },
    {
        path: "/config",
        name: "config",
        component: () => import("@/mobileView/product/dataCockpit/detail/hr/config.vue"),
    },
    {
        path: "/productionData",
        name: "productionData",
        component: () => import("@/mobileView/product/dataCockpit/detail/productionData/index.vue"),
    },
    {
        path: "/dyeingAppendAnalysis",
        name: "dyeingAppendAnalysis",
        component: () => import("@/mobileView/product/dataCockpit/detail/DyeingAppendAnalysis/index.vue"),
    },
]
export { dataCockpitRoutes };
