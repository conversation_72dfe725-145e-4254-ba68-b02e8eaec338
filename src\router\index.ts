import { createRouter, createWeb<PERSON>ashHistory, RouteRecordRaw } from "vue-router";

import { getToken, isTokenValid } from "@/api/token";
import { dataCockpitRoutes } from './dataCockpit/index'
const routes: Array<RouteRecordRaw> = [
    {
        path: "/",
        name: "moblieIndex",
        component: () => import("../mobileView/main/index.vue"),
        children: [
            {
                path: "/", // 这里设置为重定向
                redirect: { name: "shuaka" }, // 重定向到 shuaka 路由
                meta: {
                    keepAlive: true,
                    requiresAuth: true, // 需要认证的路由
                },
            },
            {
                path: "/product", // 新增 product 父路由
                name: "product",
                component: () => import("../mobileView/product/index.vue"), // 你需要创建这个组件
                children: [
                    {
                        path: "", // 子路由
                        name: "productChild",
                        component: () => import("../mobileView/product/shuaka/index.vue"),
                        meta: {
                            keepAlive: true,
                            requiresAuth: true, // 需要认证的路由
                        },
                    },
                    {
                        path: "shuaka", // 子路由
                        name: "shuaka",
                        component: () => import("../mobileView/product/shuaka/index.vue"),
                        meta: {
                            keepAlive: true,
                            requiresAuth: true, // 需要认证的路由
                        },
                    },
                    {
                        path: "pzdj", // 子路由
                        name: "pzdj",
                        component: () => import("../mobileView/product/pzdj/index.vue"),
                        meta: {
                            keepAlive: true,
                            requiresAuth: true, // 需要认证的路由
                        },
                    },

                    {
                        path: "packagingDetail",
                        name: "packagingDetail",
                        component: () => import("../mobileView/product/packagingDetail/index.vue"),
                        children: [
                            {
                                path: "",
                                name: "packagingDetailChild",
                                component: () => import("../mobileView/product/packagingDetail/packagingDetailRs/index.vue"),
                                meta: {
                                    keepAlive: true,
                                    requiresAuth: true
                                }
                            },
                            {
                                path: "packagingDetailRs",
                                name: "packagingDetailRs",
                                component: () => import("../mobileView/product/packagingDetail/packagingDetailRs/index.vue"),
                                meta: {
                                    keepAlive: true,
                                    requiresAuth: true
                                }
                            },
                            {
                                path: "packagingDetailTh",
                                name: "packagingDetailTh",
                                component: () => import("../mobileView/product/packagingDetail/packagingDetailTh/index.vue"),
                                meta: {
                                    keepAlive: true,
                                    requiresAuth: true
                                }
                            }
                        ]
                    },

                    {
                        path: "vatInvoice",
                        name: "vatInvoice",
                        component: () => import("../mobileView/product/vatInvoice/index.vue"),
                        meta: {
                            keepAlive: true,
                            requiresAuth: true, // 需要认证的路由
                        },
                    },
                    {
                        path: "/product/dataCockpit", // 子路由
                        name: "dataCockpit",
                        component: () => import("../mobileView/product/dataCockpit/index.vue"),
                        meta: {
                            keepAlive: true,
                            requiresAuth: true, // 需要认证的路由
                        },
                    },
                    {
                        path: "/my",
                        name: "my",
                        component: () => import("../mobileView/my/index.vue"),
                        meta: {
                            keepAlive: true,
                            requiresAuth: true, // 需要认证的路由
                        },
                    },
                ],
                meta: {
                    keepAlive: true,
                },
            },

        ],
        meta: {
            keepAlive: true,
        },
    },
    {
        path: "/login",
        name: "login",
        component: () => import("../mobileView/login/index.vue"),
    },
    {
        path: "/error",
        name: "error",
        component: () => import("../mobileView/error/index.vue"),
    },
    {
        path: "/packagingDetailRsSingle",
        name: "packagingDetailRsSingle",
        component: () => import("../mobileView/product/packagingDetail/packagingDetailRs/index.vue"),
        meta: {
            keepAlive: true,
            requiresAuth: true
        }
    },


];
for (let index = 0; index < dataCockpitRoutes.length; index++) {
    const element = dataCockpitRoutes[index];
    routes.push(element)
}

const router = createRouter({
    history: createWebHashHistory(),
    routes,
});

// 添加路由守卫
router.beforeEach((to, from, next) => {
    // 如果目标路由需要认证
    if (to.meta.requiresAuth) {
        localStorage.setItem("router", to.name.toString());
        if (isTokenValid()) {
            next(); // token 有效，继续访问
        } else {
            next({ name: "login" }); // token 无效，跳转到登录页面
        }
    } else {
        next(); // 不需要认证的路由，继续访问
    }
});

export default router;
