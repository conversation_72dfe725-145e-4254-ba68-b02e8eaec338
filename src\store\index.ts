import { createStore, StoreOptions } from 'vuex';
import { packageDetailMore, packageDetailLess } from '@/api/shuaka/packageDetailUpLoad';
// 定义状态的类型
interface State {
    phone: string;
    name: string;
    verificationCode: string;
    packDetail: Array<packageDetailLess | null>; // 这里可以考虑使用更具体的类型
    packageDetailMore: Array<packageDetailMore | null>; // 根据实际情况定义更具体的类型
}

// 创建 Vuex store 的选项
const storeOptions: StoreOptions<State> = {
    state: {
        phone: "",
        name: "",
        verificationCode: "",
        packDetail: [],
        packageDetailMore: []
    },
    getters: {
        /**
         * 查看excel包装明细
         * @param state 
         * @returns 
         */
        getPackDetail: (state) => state.packDetail,
        /**
         * 完整包装明细
         * @param state 
         * @returns 
         */
        getpackageDetailMore: (state) => state.packageDetailMore,
    },
    mutations: {
        /**
         * 更新excel包装明细
         * @param state 
         * @param packDetail 
         */
        setPackDetail(state, packDetail: Array<packageDetailLess | null>) {
            state.packDetail = packDetail;
        },
        /**
         * 生成完整包装明细
         * @param state 
         * @param packageDetailMore 
         */
        setpackageDetailMore(state, packageDetailMore: any[]) {
            state.packageDetailMore = packageDetailMore;
        }
    },
    actions: {
        // 可以在这里添加异步操作
    },
    modules: {
        // 可以在这里添加模块
    }
}

// 创建并导出 store
export default createStore<State>(storeOptions);
