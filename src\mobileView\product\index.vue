<template>
    <div class="app-container">
        <div class="header">
            <div class="header-content">
                <img src="@/assets/tupian.png" alt="Logo" class="header-logo">
                <h1 class="title">得伟信息管理系统</h1>
            </div>
            <div class="user-menu">
                <!-- 用户操作下拉菜单 -->
                <el-dropdown trigger="hover" placement="bottom-end" @command="handleCommand">
                    <div class="user-trigger">
                        <el-icon :size="20" class="user-icon">
                            <User />
                        </el-icon>
                    </div>

                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="logout">
                                <el-icon>
                                    <SwitchButton />
                                </el-icon>
                                <span>退出登录</span>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>
        <div class="from-to">
            <!-- 菜单和路由视图保持不变 -->
            <el-menu :default-active="$route.path" class="menu" mode="horizontal" text-color="black"
                active-text-color="#409eff">
                <el-menu-item index="shuaka">
                    <router-link to="/product/shuaka">生产刷卡</router-link>
                </el-menu-item>
                <el-menu-item index="vatInvoice">
                    <router-link to="/product/vatInvoice">发票上传</router-link>
                </el-menu-item>

                <el-sub-menu index="/product/packagingDetail">
                    <template #title>包装明细</template>
                    <el-menu-item index="/product/packagingDetail/packagingDetailRs">
                        <router-link to="/product/packagingDetail/packagingDetailRs">染整包装明细</router-link>
                    </el-menu-item>
                    <el-menu-item index="/product/packagingDetail/packagingDetailTh">
                        <router-link to="/product/packagingDetail/packagingDetailTh">贴合包装明细</router-link>
                    </el-menu-item>
                </el-sub-menu>
                <el-menu-item index="/product/dataCockpit">
                    <router-link to="/product/dataCockpit">数据驾驶舱</router-link>
                </el-menu-item>

                <el-menu-item index="/product/pzdj">
                    <router-link to="/product/pzdj">其他</router-link>
                </el-menu-item>



                <el-menu-item index="/my">
                    <router-link style="color: black;" to="/my">设置</router-link>
                </el-menu-item>
            </el-menu>
        </div>
        <router-view></router-view>
    </div>
</template>


<script setup lang="ts">
import { SwitchButton, User } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { logout } from '@/utils'; // 导入通用方法

/**
 * 处理下拉菜单命令事件
 * @function
 * @param {string} command - 接收的菜单命令标识
 * @example
 * handleCommand('logout') // 触发退出操作
 */
const handleCommand = (command: string) => {
    if (command === 'logout') {
        showLogoutConfirmation();
    }
}

/**
 * 退出登录确认提示
 * @function showLogoutConfirmation
 * @description 显示确认弹窗，确认后执行退出逻辑
 */
const showLogoutConfirmation = () => {
    ElMessageBox.confirm('确定要退出登录吗？', '退出提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        logout(); // 调用通用退出方法
    }).catch(() => { });
}
</script>

<style scoped lang="scss">
@import "./style.scss";
</style>
