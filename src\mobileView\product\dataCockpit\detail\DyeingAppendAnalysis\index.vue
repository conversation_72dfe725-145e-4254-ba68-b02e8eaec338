<template>
  <div class="page-container">
    <div class="header">
      <h2 class="title">染色追加分析看板</h2>
    </div>
    
    <div class="content">
      <DyeingAppendAnalysis />
    </div>
    
    <div class="footer">
      <p>数据更新时间: {{ new Date().toLocaleDateString() }}</p>
    </div>
  </div>
</template>

<script setup>
import DyeingAppendAnalysis from '@/component/dataCockpit/dataAnalysis/DyeingAppendAnalysis.vue';
</script>

<style scoped lang="scss">
@import './index.scss';

.page-container {
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.header {
  margin-bottom: 24px;
  text-align: center;
  flex-shrink: 0;
  
  .title {
    font-size: 22px;
    color: #303133;
    margin: 0;
    line-height: 1.4;
  }
  
  .subtitle {
    font-size: 14px;
    color: #606266;
    margin-top: 8px;
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  margin-bottom: 24px;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 12px;
  flex-shrink: 0;
}
</style>
