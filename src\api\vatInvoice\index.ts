import service from '@/request/index';
/**
 * 
 * @param file 
 * @returns 
 * 上传发票
 */
const uploadInvoice = async (file) => {
    const formData = new FormData();
    formData.append('model', file); // 将文件以二进制格式添加到 FormData 中，键名为 model

    return await service({
        url: "/api/Tool/UploadVatInvoice",
        method: "post",
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
/**
 * 获取历史发票列表
 * @param {number} page - 当前页码
 * @param {number} pageSize - 每页数量
 * @param {Object} [searchParams] - 搜索参数
 * @param {string} [searchParams.InvoiceNum] - 发票号码
 * @param {string} [searchParams.SellerName] - 销售方名称
 * @param {number} [searchParams.TotalAmount] - 金额
 * @param {string} [searchParams.StartDate] - 开始日期
 * @param {string} [searchParams.EndDate] - 结束日期
 */
const InvoiceList = async (
  page: number,
  pageSize: number,
    searchParams?: {
    InvoiceNum?: string
    SellerName?: string
    TotalAmount?: number
    StartDate?: string
    EndDate?: string
  }
) => {
    return await service({
        url: "https://texwell.com.cn:8321/api/Tool/GetVatInvoiceList",
        method: "get",
        params: {  // 修改为params传递参数
            page: page,
            pageSize: pageSize,
            ...searchParams
        }
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
export { InvoiceList, uploadInvoice };

