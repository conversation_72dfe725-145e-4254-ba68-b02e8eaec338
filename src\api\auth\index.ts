import service from "@/request";
import { formatTime } from '@/utils'
import { ElMessage } from "element-plus";
/**
 * 
 * @param username 
 * @param password 
 * @returns 
 * 登录>获取token>保存token在localstorage中
 */
const login = async (username, password) => {
    return await service({
        url: `/api/Login/JWTToken3.0?name=${username}&pass=${password}`,
        method: "get"
    }).then((val) => {
        // 检查返回的成功状态
        if (val.data.success === false) {
            // 返回一个拒绝的期约
            return Promise.reject(new Error('用户名或密码错误'));
        }
        return val; // 返回成功的响应
    }).catch((error) => {
        return Promise.reject(error); // 返回捕获的错误
    });
};
/**
 * 
 * @param token 
 * @returns 
 * 刷新token
 */
const refresh = async (token) => {
    return await service({
        url: `/api/Login/RefreshToken?token=${token.replace("Bearer ", "")}`,
        method: "get"
    }).then((val) => {
        // 检查返回的成功状态
        if (val.data.success === false) {
            // 返回一个拒绝的期约
            console.log(val);
            // return Promise.reject(new Error('token已经失效，请重新登录'));
        } else {
            let tokenReal =
            {
                time: formatTime(Date.now(), "yyyy-MM-dd HH:mm:ss"),
                val: `${val.data.response.token_type} ${val.data.response.token}`
            }
            localStorage.setItem('token', tokenReal.val);
            localStorage.setItem("tokenTime", tokenReal.time);
            return val; // 返回成功的响应
        }

    }).catch((error) => {
        return Promise.reject(error); // 返回捕获的错误
    });
}

const oaSpk = async (data: string, api_secret = "5C43D27BD514467891FA9958F5792F38") => {
    return await service({
        url: `/api/OA/GetEncryptData?data=${data}&api_secret=${api_secret}`,
        method: "get"
    }).then((val) => {
        // 检查返回的成功状态
        if (val.data.success === false) {
            // 返回一个拒绝的期约
            return Promise.reject(val);
        }
        return val; // 返回成功的响应
    }).catch((error) => {
        return Promise.reject(error); // 返回捕获的错误
    });
}

const handleLogin = async (username, password) => {
    try {
        const val = await login(username, password);
        let token = {
            time: formatTime(Date.now(), "yyyy-MM-dd HH:mm:ss"),
            val: `${val.data.response.token_type} ${val.data.response.token}`
        };
        localStorage.setItem('token', token.val);
        localStorage.setItem("tokenTime", token.time);
        localStorage.setItem("username", username);
        localStorage.setItem("password", password);
        return val;
    } catch (error) {
        ElMessage.error({
            message: error || '登录失败，请检查用户名和密码',
        });
    }
};

export { login, refresh, oaSpk, handleLogin };
