<template>
    <div class="main">
        <GkBaseInfo ref="gkBaseInfoRef"></GkBaseInfo>
        <div style="background-color: aliceblue;height: 800px;"></div>
        <UpLoad class="upload" @uploadSuccess="handleUploadSuccess" @submitSuccess="handleSubmitSuccess"
            :bcpinfo="bcpinfo"></UpLoad>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UpLoad from '../packagingDetail/upLoad/index.vue'
import GkBaseInfo from '../packagingDetail/gkBaseInfo/index.vue'

/**
 * GkBaseInfo 组件的引用
 */
const gkBaseInfoRef = ref()

/**
 * 获取 bcpinfo 状态
 */
const bcpinfo = computed(() => {
    return gkBaseInfoRef.value?.bcpinfo
})

/**
 * 处理文件上传成功事件
 */
const handleUploadSuccess = () => {
    // 调用 GkBaseInfo 组件的 fillIn 方法
    // gkBaseInfoRef.value?.fillIn()
}

/**
 * 处理保存成功事件，清除成品/半成品信息
 */
const handleSubmitSuccess = () => {
    // 清除成品/半成品选择
    gkBaseInfoRef.value?.clearBcpInfo()
}
</script>

<style scoped lang="scss">
.main {
    position: relative;
    /* 确保子元素的绝对定位相对于这个容器 */
    padding-bottom: 60px;
    /* 给底部留出空间，避免内容被遮挡 */
    padding-top: 0px;
}

.upload {
    position: fixed;
    /* 固定在视口底部 */
    bottom: 0;
    /* 距离底部0 */
    left: 0;
    /* 距离左边0 */
    right: 0;
    /* 距离右边0 */
    background-color: white;
    /* 背景颜色，确保可见 */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    /* 添加阴影效果 */
    z-index: 1000;
    /* 确保在其他元素之上 */
}
</style>
