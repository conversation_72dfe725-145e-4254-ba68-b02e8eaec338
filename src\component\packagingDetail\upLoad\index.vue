<template>
    <div>
        <el-upload class="upload-demo" drag action="" :before-upload="beforeUpload" :on-change="handleChange"
            :show-file-list="false" :file-list="fileList"> <!-- 绑定文件列表 -->
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">只能上传 Excel 文件</div>
            <div v-if="uploadedFileName" class="uploaded-file-name">{{ uploadedFileName }}</div>
        </el-upload>
        <div class="flex-container">
            <submit @success="onSubmitSuccess" :bcpinfo="props.bcpinfo"></submit>
            <el-button type="danger" @click="clearExcelData">清除</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useStore } from 'vuex';
import * as XLSX from 'xlsx';
import { ElMessage, ElMessageBox } from 'element-plus';
import submit from '@/component/packagingDetail/submit/index.vue'

/**
 * 接收 props
 */
const props = defineProps<{
    bcpinfo?: string | null
}>()

/**
 * 定义触发的事件
 */
const emit = defineEmits(['uploadSuccess', 'submitSuccess'])

/**
 * 使用 Vuex store
 */
const store = useStore();

/**
 * excel数据
 */
const excelData = ref<any[]>([]);

/**
 * excel数据转json
 */
const jsonData = ref([]);

/**
 * 上传的文件名
 */
const uploadedFileName = ref('');
const fileList = ref([]); // 新增状态来管理文件列表

/**
 * 校验文件是否是excel
 * @param file 
 */
const beforeUpload = (file: File) => {
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
        ElMessage.error('只能上传 Excel 文件!');
    }
    return isExcel;
};

/**
 * 上传文件
 * @param file 
 */
const handleChange = (file: any) => {
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // 获取数据，第一行作为键名
        const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        const headers: string[] = rows[0] as string[];
        const dataRows = rows.slice(1);

        // 过滤掉工卡号为空、null、undefined、空格等无效值的行
        const validDataRows = dataRows.filter((row) => {
            const 工卡号 = row[headers.indexOf('工卡号')];
            // 检查工卡号是否为有效值
            return 工卡号 !== null &&
                工卡号 !== undefined &&
                工卡号 !== '' &&
                String(工卡号).trim() !== '';
        });

        if (validDataRows.length === 0) {
            ElMessage.error('Excel 文件中没有有效的工卡号数据！');
            return;
        }

        if (!check(validDataRows, headers)) {
            return
        }
        excelData.value = validDataRows.map((row, index) => {
            const rowData = {};
            headers.forEach((header, i) => {
                rowData[header] = row[i];
            });
            return rowData;
        });
        uploadedFileName.value = file.name;
        fileList.value = [file]; // 更新文件列表
    };
    reader.readAsArrayBuffer(file.raw);
    setTimeout(() => {
        showExcelData();
    }, 100);
};
/**
 * 校验excel列名和数据是否规范
 * @param dataRows 
 * @param headers 
 */
const check = (dataRows: any, headers: any) => {
    // 检查列头是否包含所需字段
    const requiredHeaders = ['工卡号', '匹号', '公斤米长', '重量', '等级'];
    const isValidHeaders = requiredHeaders.every(header => headers.includes(header));

    if (!isValidHeaders) {
        ElMessage.error('Excel 文件的列头必须包含：工卡号、匹号、公斤米长、重量、等级');
        return false;
    }

    // 验证数据
    const errors: string[] = [];
    dataRows.forEach((row, index) => {
        const rowData = {};
        headers.forEach((header, i) => {
            rowData[header] = row[i];
        });

        const 工卡号 = rowData['工卡号'];
        const 匹号 = rowData['匹号'];
        const 公斤米长 = rowData['公斤米长'];
        const 重量 = rowData['重量'];

        // 检查匹号、公斤米长、重量是否为数字
        if (isNaN(Number(匹号))) {
            errors.push(`工卡号 ${工卡号} 在第 ${index + 2} 行的匹号必须是数字`);
        }
        if (isNaN(Number(公斤米长))) {
            errors.push(`工卡号 ${工卡号} 在第 ${index + 2} 行的公斤米长必须是数字`);
        }
        if (isNaN(Number(重量))) {
            errors.push(`工卡号 ${工卡号} 在第 ${index + 2} 行的重量必须是数字`);
        }
    });

    // 如果有错误，提示用户
    if (errors.length > 0) {
        ElMessage.error(errors.join('; '));
        return false;
    } else {
        return true
    }
}

/**
 * 调用 Vuex mutation 更新 packDetail
 */
const showExcelData = () => {
    jsonData.value = excelData.value;
    store.commit('setPackDetail', jsonData.value); // 调用 Vuex mutation 更新 packDetail
    emit('uploadSuccess'); // 触发文件上传成功事件
};

const clearExcelData = () => {
    ElMessageBox.confirm("您确定要清除数据吗？", "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            try {
                store.commit('setPackDetail', []);
                store.commit("setpackageDetailMore", []);
                uploadedFileName.value = ''; // 清空上传的文件名
                fileList.value = []; // 清空文件列表
                ElMessage.success("数据已成功清除！");
            } catch (error) {
                ElMessage.error(`清除失败，请刷新后再试 - ${String(error)}`);
            }
        })
        .catch(() => {
            ElMessage.info("清除操作已取消。");
        });
};

/**
 * 监听 submit 组件的成功事件，自动执行清除操作
 */
const onSubmitSuccess = () => {
    // 直接执行清除操作，不需要确认
    try {
        store.commit('setPackDetail', []);
        store.commit("setpackageDetailMore", []);
        uploadedFileName.value = ''; // 清空上传的文件名
        fileList.value = []; // 清空文件列表
        ElMessage.success("数据已自动清除！");

        // 通知父组件保存成功，以便清除成品/半成品信息
        emit('submitSuccess');
    } catch (error) {
        ElMessage.error(`自动清除失败 - ${String(error)}`);
    }
};
</script>

<style scoped lang="scss">
.upload-demo {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.uploaded-file-name {
    margin-top: 10px;
    color: #409EFF;
}

.flex-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    /* 添加上下左右内边距 */
    gap: 16px;
    /* 按钮之间的间距 */
}
</style>
