import service from '@/request/index'
/**
 * 
 * @returns 
 * 获取部门信息
 */
const department = async () => {
    return await service({
        url: "/api/Visitor/GetDepartmentList",
        method: "get"
    }).then((val) => {
        return val
    }).catch((error) => {

        return error
    })
}
/**
 * 
 * @param departmentid 
 * @param key 
 * @returns 
 * 获取人员
 */
const member = async (departmentid, key) => {
    return await service({
        url: `/api/Visitor/GetInterviewee?departmentid=${departmentid}&key=${key}`,
        method: "get"
    }).then((val) => {
        return val
    }).catch((error) => {
        return error
    })
}
export {
    department, member
}