<template>
    <div>
        <div class="title-style" :style="{ backgroundColor: titleColor }">
            <h3>{{ title }}</h3>
        </div>
        <table border="1" cellspacing="0" cellpadding="5">
            <thead>
                <tr>
                    <th>{{ smallTitle.firstTitle }}</th>
                    <th>{{ smallTitle.secondTitle }}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="font-weight: bolder;font-size: large;" class="total">{{ data.one }}</td>
                    <td style="font-weight: bolder;font-size: large;">{{ data.two }}</td>
                </tr>
                <tr>
                    <td class="I">{{ data.three }}</td>
                    <td>{{ data.four }}</td>
                </tr>
                <tr>
                    <td class="II">{{ data.five }}</td>
                    <td>{{ data.six }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { DataProps } from './interface';
const props = defineProps({
    title: {
        type: String,
        required: false,
        default: '三、生产情况'
    },
    smallTitle: {
        type: Object,
        required: false,
        default: {
            firstTitle: '当日投染量(单位:吨)',
            secondTitle: '当月累计投染量(单位:吨)',
        }
    },
    data: {
        type: Object as PropType<DataProps>,
        required: false,
        default: () => ({ todayOrder: 0, monthOrder: 0 })
    },
    titleColor: {
        type: String,
        required: true,

    }

})
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>