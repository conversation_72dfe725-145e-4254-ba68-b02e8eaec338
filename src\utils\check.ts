import { ElMessage } from "element-plus";
import { jg } from '@/mock/constant';
const fuelPlatePattern = /^[\u4E00-\u9FA5][A-Z][A-Z0-9]{5}$/i; // 普通车
const newEnergyPlatePattern = /^[\u4E00-\u9FA5][A-Z]{2}[A-Z0-9]{5}$/i; // 新能源车
const phoneRegex = /^1[3-9]\d{9}$/;// 正则表达式，匹配中国大陆手机号
const idCardRegex = /^(?:\d{15}|\d{17}[\dX])$/;// 正则表达式，匹配中国大陆身份证号
/**
 * 
 * @param {object}data 表单数据
 * @param {boolean}cp 是否有车牌
 * @returns 
 * 校验表单完整性
 */
function validateVisitData(data: object, cp: boolean) {
    const requiredFields: any = {
        fklx1: '到访模式',
        fklx2: '访问类型',
        bfrxm: '拜访人',
        // bfrbm: '拜访人部门',
        fkxm: '您的姓名',
        fksj: '您的手机号',
        lfsy: '来访事由',
        fwkssj: "来访时间",
        fwjssj: "结束时间"
    };
    if (cp) {
        requiredFields.fkcp = "车牌号"
    } else {
        // requiredFields.fksfzh = "身份证号"
        // requiredFields.sfcdwdcwp = "携带物品"
    }
    for (const [key, label] of Object.entries(requiredFields)) {
        if (data[key] === null || data[key] === "") {

            return [false, `${label} 填写错误`]; // 只要有一个字段不满足条件就返回 false
        }
    }

    return [true, ""]; // 所有字段都满足条件时返回 true
}
/**
 * 
 * @param data 
 * 校验车牌号
 */
const validatePlateNumber = (data: any) => {
    data.fkcp = data.fkcp.replace(/[\s\u3000]+/g, "");
    if (fuelPlatePattern.test(data.fkcp) || newEnergyPlatePattern.test(data.fkcp) || data.fkcp == "") {
        data.fkcp = data.fkcp.toUpperCase();
    } else {
        data.fkcp = ""
        ElMessage.error('请输入有效车牌号')
    }
};
/**
 * 
 * @param data 
 * 校验手机号
 */
const validatePhone = (data: any) => {
    if (phoneRegex.test(data.fksj) || data.fksj == "") {
    } else {
        data.fksj = ""
        ElMessage.error('请输入有效手机号')
    }
};
/**
 * 
 * @param data 
 * 校验手机号
 */
const validateSfzh = (data: any) => {
    if (idCardRegex.test(data.fksfzh) || data.fksfzh == "") {
    } else {
        data.fksfzh = ""
        ElMessage.error('请输入有效身份证号')
    }
};
/**
 * 
 * @param variable 
 * @returns 
 * 校验验证码有效性
 */
const isSixDigitString = variable =>
    /^\d{6}$/.test(String(variable));
/**
 * 
    * @param rule
    * @param value
    * @param callback
    * 校验手机号
    * @returns
    *   校验通过返回 callback()，校验失败返回 callback(new Error('错误信息'))
    */
const validatePhoneElTable = (rule, value, callback) => {
    const phoneRegex = /^1[3-9]\d{9}$/; // 正则表达式，匹配中国大陆手机号
    if (!value) {
        callback(new Error('请输入手机'));
    } else if (!phoneRegex.test(value)) {
        callback(new Error('手机号错误'));
    } else {
        callback(); // 校验通过
    }
}
/**
 * 
 * @param idCardNumber 
 * @returns 
 * 校验身份证号有效性
 */
const checkIdCardValidity = (idCardNumber: string): boolean => {
    // 这里添加身份证号的校验逻辑
    // 返回 true 或 false
    // 示例：简单长度校验
    // 检查长度
    // 检查长度
    if (idCardNumber.length !== 18) {
        return false;
    }

    // 正则表达式检查格式
    const regex = /^(?:\d{17}[\dX])$/;
    if (!regex.test(idCardNumber)) {
        return false;
    }

    // 提取出生日期
    const birthDate = idCardNumber.substring(6, 14);
    const year = parseInt(birthDate.substring(0, 4), 10);
    const month = parseInt(birthDate.substring(4, 6), 10);
    const day = parseInt(birthDate.substring(6, 8), 10);

    // 检查日期有效性
    const isValidDate = (year > 1900 && month >= 1 && month <= 12 && day >= 1 && day <= 31);
    if (!isValidDate) {
        return false;
    }

    // 检查每个月的天数
    const daysInMonth = [31, (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    if (day > daysInMonth[month - 1]) {
        return false;
    }

    // 加权因子
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkDigits = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

    // 计算校验和
    let sum = 0;
    for (let i = 0; i < 17; i++) {
        sum += parseInt(idCardNumber[i]) * weights[i];
    }

    // 计算校验位
    const checkIndex = sum % 11;
    const checkDigit = checkDigits[checkIndex];

    // 校验位对比
    return checkDigit === idCardNumber[17].toUpperCase();
};
/**
 *  
 * @param idCardNumber
 * @returns
 *  提取身份证号中的出生日期
 */
const extractBirthDate = (idCardNumber: any) => {
    // 检查身份证号的长度
    if (idCardNumber.length !== 18) {
        throw new Error("身份证号必须为18位");
    }

    // 提取出生日期部分
    const birthDateStr = idCardNumber.substring(6, 14);

    // 格式化为 YYYY-MM-DD
    const year = birthDateStr.substring(0, 4);
    const month = birthDateStr.substring(4, 6);
    const day = birthDateStr.substring(6, 8);

    return `${year}-${month}-${day}`;
}

/**
 * 
 * @param idCardNumber
 * @returns
 *  提取身份证号中的籍贯
 */
const extractJg = (idCardNumber: any) => {


    // 提取身份证号前六位
    const idCardPrefix = idCardNumber.substring(0, 6);

    // 匹配籍贯
    const birthplace = jg[idCardPrefix];

    if (birthplace) {
        return birthplace
    } else {
        return ""
    }
}



export { extractJg, extractBirthDate, checkIdCardValidity, validatePhoneElTable, isSixDigitString, validateSfzh, validatePhone, validatePlateNumber, phoneRegex, fuelPlatePattern, newEnergyPlatePattern, validateVisitData }