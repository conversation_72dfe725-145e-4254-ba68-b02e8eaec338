<template>
    <div class="centerRight2">
        <div class="bg-color-black">
            <div class="d-flex pt-2 pl-2">
                <span>
                    <i class="iconfont icon-vector" />
                </span>
                <div class="d-flex">
                    <span class="fs-xxxx text mx-2">离职类别</span>
                </div>
            </div>
            <div class="d-flex mt-1 jc-center body-box">
                <dv-scroll-board class="dv-scr-board" :config="config" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue'

const config = reactive({
    header: ['原因', '占比'],
    data: [
        ['离职原因1', "<span  class='colorGrass'>35%</span>"],
        ['离职原因2', "<span  class='colorGrass'>10%</span>"],
        ['离职原因3', "<span  class='colorGrass'>10%</span>"],
        ['离职原因4', "<span  class='colorGrass'>30%</span>"],
        ['离职原因5', "<span  class='colorGrass'>15%</span>"],

    ],
    rowNum: 7, //表格行数
    headerHeight: 35,
    headerBGC: '#0f1325', //表头
    oddRowBGC: '#0f1325', //奇数行
    evenRowBGC: '#171c33', //偶数行
    index: true,
    columnWidth: [50],
    align: ['center']
})

</script>

<style lang="scss" scoped>
$box-height: 410px;
$box-width: 300px;

.centerRight2 {
    padding: 16px;
    padding-top: 20px;
    height: $box-height;
    width: $box-width;
    border-radius: 5px;

    .bg-color-black {
        height: $box-height - 30px;
        border-radius: 10px;
    }

    .text {
        color: #c3cbde;
    }

    .body-box {
        border-radius: 10px;
        overflow: hidden;

        .dv-scr-board {
            width: 270px;
            height: 340px;
        }
    }
}
</style>
