<template>
    <div>
        <el-button type="primary" @click="submit">保存</el-button>
    </div>
</template>
<script setup lang="ts">
import { PackageDetailSubmit } from '@/api/shuaka/packageDetailUpLoad';
import { useStore } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';

/**
 * 接收 props
 */
const props = defineProps<{
    bcpinfo?: string | null
}>()

const store = useStore()
const emit = defineEmits(['success'])

const submit = () => {
    // 检查是否选择了成品/半成品
    if (!props.bcpinfo) {
        ElMessageBox.alert('请先选择成品或半成品！', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
        })
        return
    }

    // 检查是否已补全明细
    if (!store.state.packageDetailMore || store.state.packageDetailMore.length === 0) {
        ElMessageBox.alert('请先补全完整明细！', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
        })
        return
    }

    PackageDetailSubmit(store.state.packageDetailMore).then((val) => {
        store.commit("setpackageDetailMore", [])
        store.commit("setPackDetail", [])
        if (val.data.success) {
            ElMessage.success(val.data.msg)
            emit('success') // 触发成功事件
        } else {
            ElMessageBox.alert(val.data.msg)
        }
    }).catch((error) => {
        ElMessage.error(String(error))
    })
}
</script>
<style scoped lang="scss">
/* 可以在这里添加样式 */
</style>