// 染色生产数据分析API
// 服务器: 223.95.171.10:18090
// 功能: 看板数据查询和分析

// API配置
type ApiConfig = {
  baseURL: string;
  apiSecret: string;
};

const API_CONFIG: ApiConfig = {
  baseURL: 'http://223.95.171.10:18090/api/data/invoke/1/analysis-data/0',
  apiSecret: 'DRyEnruYbgYOGbiw'
};

// 查询参数类型
type AnalysisParams = {
  startDate?: string;
  endDate?: string;
  colorCode?: string;
  decathlonFabric?: string;
  deweiFabric?: string;
  [key: string]: any;
};

// API 响应类型
type ApiResponse<T = any> = {
  code: number;
  msg?: string;
  data: T;
};

type DyeingData = {
  id: number;
  colorCode: string;
  dyeDate: string;
  decathlonFabric: string;
  deweiFabric: string;
  batchCapacity: number;
  result: number;
  batchNumber: string;
  workerCode: string;
  machineCode: string;
  seriesNo: string;
  year: number;
  month: number;
  dyeingConsistency: number;
  timeRFT: number;
  colorRFT: number;
  cfRFT: number;
  inspectionResult: number;
  mainDefect: string | null;
  createTime: string;
  updateTime: string;
  dataVersion: number;
};

export function getAnalysisData(params: AnalysisParams = {}): Promise<ApiResponse> {
  const requestParams = {
    api_secret: API_CONFIG.apiSecret,
    ...params
  };
  const urlParams = new URLSearchParams(requestParams as Record<string, string>);
  const fullURL = `${API_CONFIG.baseURL}?${urlParams.toString()}`;
  return fetch(fullURL, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }).then(data => data)
    .catch(error => {
      console.error('API调用失败:', error);
      throw error;
    });
}

export function healthCheck(): Promise<ApiResponse> {
  return getAnalysisData({ startDate: '2025-01-01', endDate: '2025-01-01' })
    .then(response => ({
      code: response.code === 0 ? 200 : 500,
      msg: response.code === 0 ? 'success' : 'error',
      data: {
        status: response.code === 0 ? 'healthy' : 'error',
        database: response.code === 0 ? 'connected' : 'disconnected',
        lastDataUpdate: new Date().toISOString(),
        version: '1.0.0'
      }
    }))
    .catch(() => ({
      code: 500,
      msg: 'API connection failed',
      data: {
        status: 'error',
        database: 'disconnected',
        lastDataUpdate: null,
        version: '1.0.0'
      }
    }));
}

export function transformApiDataToFrontend(apiData: any[]): DyeingData[] {
  if (!Array.isArray(apiData)) {
    return [];
  }
  return apiData.map(item => ({
    id: Number(item.ID) || 0,
    colorCode: item.colorCode || '',
    dyeDate: item.dyeDate ? item.dyeDate.split(' ')[0] : '',
    decathlonFabric: item.decathlonFabric || '',
    deweiFabric: item.deweiFabric || '',
    batchCapacity: Number(item.batchCapacity || item.Batch_Capacity || item.tankCapacity || item.Tank_Capacity) || 0,
    result: item.result === "1" || item.result === 1 ? 1 : 0,
    batchNumber: item.batchNumber || '',
    workerCode: item.workerCode || '',
    machineCode: item.machineCode || '',
    seriesNo: item.seriesNo || '',
    year: Number(item.year) || new Date().getFullYear(),
    month: Number(item.month) || new Date().getMonth() + 1,
    dyeingConsistency: item.dyeingConsistency === "true" || item.dyeingConsistency === true ? 1 : 0,
    timeRFT: item.timeRFT === "true" || item.timeRFT === true ? 1 : 0,
    colorRFT: item.colorRFT === "true" || item.colorRFT === true ? 1 : 0,
    cfRFT: item.cfRFT === "true" || item.cfRFT === true ? 1 : 0,
    inspectionResult: item.inspectionResult === "true" || item.inspectionResult === true ? 1 : 0,
    mainDefect: item.mainDefect || null,
    createTime: item.createTime || '',
    updateTime: item.updateTime || '',
    dataVersion: Number(item.dataVersion) || 1
  }));
}

export async function getDataStatistics(params: AnalysisParams = {}): Promise<ApiResponse> {
  try {
    const response = await getAnalysisData(params);
    if (response.code !== 0 || !Array.isArray(response.data)) {
      throw new Error('获取数据失败');
    }
    const transformedData = transformApiDataToFrontend(response.data);
    const totalRecords = transformedData.length;
    const uniqueColors = new Set(transformedData.map(item => item.colorCode).filter(Boolean)).size;
    const uniqueDecathlonFabrics = new Set(transformedData.map(item => item.decathlonFabric).filter(Boolean)).size;
    const uniqueDeweiFabrics = new Set(transformedData.map(item => item.deweiFabric).filter(Boolean)).size;
    const passCount = transformedData.filter(item => item.result === 1).length;
    const failCount = transformedData.filter(item => item.result === 0).length;
    const overallPassRate = totalRecords > 0 ? parseFloat(((passCount / totalRecords) * 100).toFixed(1)) : 0;
    const dates = transformedData.map(item => item.dyeDate).filter(Boolean).sort();
    const earliestDate = dates.length > 0 ? dates[0] : null;
    const latestDate = dates.length > 0 ? dates[dates.length - 1] : null;
    return {
      code: 200,
      msg: 'success',
      data: {
        totalRecords,
        uniqueColors,
        uniqueDecathlonFabrics,
        uniqueDeweiFabrics,
        passCount,
        failCount,
        overallPassRate,
        earliestDate,
        latestDate,
        lastUpdateTime: new Date().toISOString()
      }
    };
  } catch (error: any) {
    console.error('计算统计数据失败:', error);
    return {
      code: 500,
      msg: error.message || '计算统计数据失败',
      data: null
    };
  }
} 