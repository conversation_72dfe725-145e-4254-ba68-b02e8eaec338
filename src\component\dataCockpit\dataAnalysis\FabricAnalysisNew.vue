<template>
  <div class="fabric-analysis-container">


    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载数据...">
      <div style="height: 200px;"></div>
    </div>

    <!-- 无数据提示 -->
    <div v-else-if="rawData.length === 0" class="no-data-container">
      <el-empty description="暂无数据，请检查API连接">
        <el-button type="primary" @click="loadDataFromAPI">重新加载</el-button>
      </el-empty>
    </div>

    <!-- 正常内容 -->
    <template v-else-if="rawData.length > 0">
      <!-- 迪卡侬面料分析组 -->
      <AnalysisChartGroup
        :raw-data="decathlonFilteredData"
        top15-title="TOP15迪卡侬面料回修率分析"
        top15-subtitle="按面料统计回修率和染色缸数"
        :top15-data="decathlonTop15Data"
        monthly-title="迪卡侬面料月度回修率分析"
        data-type="面料"
        data-field="decathlonFabric"
        v-model:start-date="decathlonStartDate"
        v-model:end-date="decathlonEndDate"
        v-model:tank-type="decathlonTankTypeFilter"
      />

      <!-- 得伟面料分析组 -->
      <AnalysisChartGroup
        :raw-data="deweiFilteredData"
        top15-title="TOP15得伟面料回修率分析"
        top15-subtitle="按面料统计回修率和染色缸数"
        :top15-data="deweiTop15Data"
        monthly-title="得伟面料月度回修率分析"
        data-type="面料"
        data-field="deweiFabric"
        v-model:start-date="deweiStartDate"
        v-model:end-date="deweiEndDate"
        v-model:tank-type="deweiTankTypeFilter"
      />
    </template>

    <!-- 数据为空时的提示 -->
    <template v-else>
      <div class="no-data-container">
        <el-empty description="暂无数据">
          <el-button type="primary" @click="loadDataFromAPI">重新加载</el-button>
        </el-empty>
      </div>
    </template>
  </div>
</template>

<script setup>
import { getAnalysisData, transformApiDataToFrontend } from '@/api/dataAnalysis/dyeingAnalysis';
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import AnalysisChartGroup from './AnalysisChartGroup.vue';

// 状态管理
const rawData = ref([]);
const loading = ref(false);

// 迪卡侬面料筛选状态
const decathlonStartDate = ref('');
const decathlonEndDate = ref('');
const decathlonTankTypeFilter = ref('all'); // 'all', 'large', 'small'

// 得伟面料筛选状态
const deweiStartDate = ref('');
const deweiEndDate = ref('');
const deweiTankTypeFilter = ref('all'); // 'all', 'large', 'small'

// 迪卡侬面料过滤后的数据
const decathlonFilteredData = computed(() => {
  // 确保rawData.value是数组
  if (!rawData.value || !Array.isArray(rawData.value) || rawData.value.length === 0) {
    return [];
  }

  let filtered = [...rawData.value]; // 创建副本避免修改原数组

  // 应用日期筛选
  if (decathlonStartDate.value || decathlonEndDate.value) {
    filtered = filtered.filter(item => {
      if (!item || !item.dyeDate) return false;

      const dyeDate = new Date(item.dyeDate);
      if (isNaN(dyeDate)) return false;

      const start = decathlonStartDate.value ? new Date(decathlonStartDate.value) : new Date(0);
      const end = decathlonEndDate.value ? new Date(decathlonEndDate.value) : new Date();

      return dyeDate >= start && dyeDate <= end;
    });
  }

  // 应用缸型筛选
  if (decathlonTankTypeFilter.value !== 'all') {
    filtered = filtered.filter(item => {
      if (!item) return false;
      const capacity = parseFloat(item.batchCapacity) || 0;
      if (decathlonTankTypeFilter.value === 'large') {
        return capacity >= 250;
      } else if (decathlonTankTypeFilter.value === 'small') {
        return capacity < 250;
      }
      return true;
    });
  }

  return filtered;
});

// 得伟面料过滤后的数据
const deweiFilteredData = computed(() => {
  // 确保rawData.value是数组
  if (!rawData.value || !Array.isArray(rawData.value) || rawData.value.length === 0) {
    return [];
  }

  let filtered = [...rawData.value]; // 创建副本避免修改原数组

  // 应用日期筛选
  if (deweiStartDate.value || deweiEndDate.value) {
    filtered = filtered.filter(item => {
      if (!item || !item.dyeDate) return false;

      const dyeDate = new Date(item.dyeDate);
      if (isNaN(dyeDate)) return false;

      const start = deweiStartDate.value ? new Date(deweiStartDate.value) : new Date(0);
      const end = deweiEndDate.value ? new Date(deweiEndDate.value) : new Date();

      return dyeDate >= start && dyeDate <= end;
    });
  }

  // 应用缸型筛选
  if (deweiTankTypeFilter.value !== 'all') {
    filtered = filtered.filter(item => {
      if (!item) return false;
      const capacity = parseFloat(item.batchCapacity) || 0;
      if (deweiTankTypeFilter.value === 'large') {
        return capacity >= 250;
      } else if (deweiTankTypeFilter.value === 'small') {
        return capacity < 250;
      }
      return true;
    });
  }

  return filtered;
});



// 计算属性 - 迪卡侬面料TOP15数据
const decathlonTop15Data = computed(() => {
  const processedData = processTop15Data('decathlonFabric', decathlonFilteredData.value);
  return {
    items: processedData.fabrics,
    batches: processedData.batches,
    repairRates: processedData.repairRates
  };
});

const deweiTop15Data = computed(() => {
  const processedData = processTop15Data('deweiFabric', deweiFilteredData.value);
  return {
    items: processedData.fabrics,
    batches: processedData.batches,
    repairRates: processedData.repairRates
  };
});

// 处理TOP15数据的通用函数 - 支持不同面料字段
const processTop15Data = (fabricField, dataSource) => {
  // 严格验证数据
  if (!dataSource || !Array.isArray(dataSource) || dataSource.length === 0) {
    return { fabrics: [], batches: [], repairRates: [] };
  }

  // 验证fabricField参数
  if (!fabricField || typeof fabricField !== 'string') {
    console.warn('processTop15Data: fabricField参数无效', fabricField);
    return { fabrics: [], batches: [], repairRates: [] };
  }

  // 统计面料数据
  const fabricStats = {};

  try {
    dataSource.forEach((item) => {
      // 验证item对象
      if (!item || typeof item !== 'object') {
        return;
      }

      const fabric = item[fabricField];
      if (fabric && typeof fabric === 'string' && fabric.trim()) {
        if (!fabricStats[fabric]) {
          fabricStats[fabric] = { total: 0, repair: 0 };
        }
        fabricStats[fabric].total += 1;
        if (item.result === 0) {
          fabricStats[fabric].repair += 1;
        }
      }
    });
  } catch (error) {
    console.error('processTop15Data forEach错误:', error);
    return { fabrics: [], batches: [], repairRates: [] };
  }
  
  // 转换为数组并计算回修率
  let fabricArray = Object.entries(fabricStats).map(([fabric, stats]) => ({
    fabric,
    batches: stats.total,
    repairCount: stats.repair,
    repairRate: stats.total > 0 ? parseFloat(((stats.repair / stats.total) * 100).toFixed(1)) : 0
  }));
  
  if (fabricArray.length === 0) {
    return { fabrics: [], batches: [], repairRates: [] };
  }
  
  // 取TOP15（按总缸数排序）
  const top15 = [...fabricArray]
    .sort((a, b) => b.batches - a.batches)
    .slice(0, 15);
  
  // 按回修率从高到低排序
  const sortedByRepairRate = [...top15]
    .sort((a, b) => b.repairRate - a.repairRate);
  

  
  return {
    fabrics: sortedByRepairRate.map(item => item.fabric),
    batches: sortedByRepairRate.map(item => item.batches),
    repairRates: sortedByRepairRate.map(item => item.repairRate)
  };
};







// 从API加载数据
const loadDataFromAPI = async (params = {}) => {
  try {
    loading.value = true;
    const response = await getAnalysisData(params);

    // 适配新的API响应格式 (code: 0 表示成功)
    if (response.code === 0 && Array.isArray(response.data)) {
      const transformedData = transformApiDataToFrontend(response.data);

      // 确保数据是数组格式
      if (Array.isArray(transformedData)) {
        rawData.value = transformedData;
        ElMessage.success(`面料分析已加载${transformedData.length}条数据`);
      } else {
        console.error('transformedData不是数组:', transformedData);
        rawData.value = [];
        ElMessage.error('数据格式错误');
      }
    } else {
      throw new Error(response.msg || '获取数据失败');
    }
  } catch (error) {
    console.error('面料分析数据加载失败:', error);
    ElMessage.error(`加载数据失败: ${error.message}`);
    rawData.value = [];
  } finally {
    loading.value = false;
  }
};



// 组件挂载时加载数据
onMounted(async () => {
  try {
    // 默认加载最近1年的数据
    const endDate = new Date();
    const startDate = new Date();
    startDate.setFullYear(startDate.getFullYear() - 1);

    await loadDataFromAPI({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    });
  } catch (error) {
    ElMessage.error('数据加载失败，请检查网络连接');
  }
});

// 暴露方法给父组件
defineExpose({
  loadDataFromAPI
});
</script>

<style scoped lang="scss">
.fabric-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;
  min-height: 500px;
}



.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background: white;
  border-radius: 8px;
}
</style>
