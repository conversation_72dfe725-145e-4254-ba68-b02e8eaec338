<!--
  面料色号组成分析图表组件
  
  主要功能：
  1. 显示指定面料的色号组成分析 - 显示该面料包含哪些色号及其数量和回修率
  2. 双Y轴图表 - 左轴显示缸数（柱状图），右轴显示回修率（折线图）
  3. 点击TOP15面料图表后触发显示
  4. 数据为空时显示提示信息
  
  Props:
  - title: 图表标题
  - selectedFabric: 当前选中的面料名称
  - colorAnalysisData: 色号分析数据数组，包含colorCode、batches、repairRate字段
  - noDataMessage: 无数据时的提示信息
  
  特点：
  - 使用ECharts渲染图表
  - 支持响应式布局
  - 美观的渐变色彩和动画效果
  - 详细的数据提示框
-->
<template>
  <div class="chart-card">
    <div class="chart-header">
      <div class="chart-title">{{ title }}</div>
      <div class="chart-subtitle">
        {{ colorAnalysisData.length ? `面料: ${selectedFabric}` : placeholder }}
      </div>
    </div>
    <div class="chart-container">
      <div v-if="colorAnalysisData.length" ref="chartRef" style="height: 500px; width: 100%;"></div>
      <div v-else class="no-data">
        <i class="el-icon-warning-outline"></i>
        <div class="no-data-message">{{ noDataMessage }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted, onUnmounted, ref, watch } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  selectedFabric: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请点击左侧面料图表查看色号组成'
  },
  colorAnalysisData: {
    type: Array,
    default: () => []
  },
  noDataMessage: {
    type: String,
    default: '请点击左侧面料图表查看色号组成分析'
  }
});

const chartRef = ref(null);
let chartInstance = null;

// 初始化图表实例
const initChart = () => {
  if (!chartRef.value) {
    return;
  }
  
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  chartInstance = echarts.init(chartRef.value);
  
  // 监听窗口变化
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };
  
  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
};

// 渲染图表数据
const renderChart = () => {
  if (!chartInstance || !props.colorAnalysisData.length) {
    return;
  }
  
  const colors = props.colorAnalysisData.map(item => item.colorCode);
  const batches = props.colorAnalysisData.map(item => item.batches);
  const repairRates = props.colorAnalysisData.map(item => item.repairRate);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: '#ddd',
      borderWidth: 1,
      padding: 15,
      textStyle: {
        color: '#333',
        fontSize: 14
      },
      formatter: function(params) {
        const batchParam = params[0];
        const rateParam = params[1];
        const colorData = props.colorAnalysisData[batchParam.dataIndex];
        
        return `
          <div style="margin-bottom:8px;font-size:16px;font-weight:bold;color:#333">${batchParam.name}</div>
          <div style="display:flex;align-items:center;margin-bottom:5px;">
            <div style="width:12px;height:12px;background:#2c6bcd;margin-right:8px;border-radius:2px;"></div>
            <span style="color:#666;font-size:14px;">染色缸数: </span>
            <span style="font-weight:bold;font-size:16px;margin-left:5px;color:#2c6bcd;">${batchParam.value}</span>
          </div>
          <div style="display:flex;align-items:center;margin-bottom:5px;">
            <div style="width:12px;height:12px;background:#ee6666;margin-right:8px;border-radius:2px;"></div>
            <span style="color:#666;font-size:14px;">回修率: </span>
            <span style="font-weight:bold;font-size:16px;margin-left:5px;color:#ee6666;">${rateParam.value}%</span>
          </div>
          <div style="display:flex;align-items:center;">
            <span style="color:#666;font-size:14px;">回修次数: </span>
            <span style="font-weight:bold;font-size:16px;margin-left:5px;color:#333;">${colorData.repairCount || 0}</span>
          </div>
        `;
      }
    },
    legend: {
      data: ['染色缸数', '回修率'],
      bottom: 10,
      itemGap: 30,
      textStyle: {
        fontSize: 14
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: colors,
      axisLine: {
        lineStyle: {
          color: '#606266',
          width: 1
        }
      },
      axisLabel: {
        interval: 0,
        rotate: 45,
        fontSize: 12,
        fontWeight: 'bold'
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '染色缸数',
        nameTextStyle: {
          color: '#2c6bcd',
          fontSize: 14,
          fontWeight: 'bold',
          padding: [0, 50, 0, 0]
        },
        axisLine: {
          lineStyle: {
            color: '#2c6bcd'
          }
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        },
        min: 0,
        max: value => Math.ceil(value.max * 1.1)
      },
      {
        type: 'value',
        name: '回修率',
        nameTextStyle: {
          color: '#ee6666',
          fontSize: 14,
          fontWeight: 'bold',
          padding: [0, 0, 0, 50]
        },
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#ee6666'
          }
        },
        axisLabel: {
          formatter: '{value}%',
          fontSize: 12
        },
        splitLine: { 
          show: false
        }
      }
    ],
    series: [
      {
        name: '染色缸数',
        type: 'bar',
        barWidth: '40%',
        data: batches,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#5d9cfc' },
            { offset: 0.5, color: '#2c6bcd' },
            { offset: 1, color: '#1a4c8c' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 12,
          color: '#2c6bcd',
          fontWeight: 'bold'
        }
      },
      {
        name: '回修率',
        type: 'line',
        symbolSize: 8,
        yAxisIndex: 1,
        data: repairRates,
        lineStyle: {
          width: 3,
          color: '#ee6666'
        },
        itemStyle: {
          color: '#ee6666',
          borderWidth: 2,
          borderColor: '#fff'
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          fontSize: 12,
          color: '#ee6666',
          fontWeight: 'bold'
        },
        z: 10
      }
    ]
  };
  
  chartInstance.setOption(option);
};

// 监听数据变化
watch(() => props.colorAnalysisData, () => {
  setTimeout(() => {
    if (!chartInstance) {
      initChart();
    }
    renderChart();
  }, 100);
}, { deep: true });

onMounted(() => {
  setTimeout(() => {
    initChart();
    renderChart();
  }, 200);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped lang="scss">
.chart-card {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-5px);
  }
  
  .chart-header {
    padding: 20px 20px 15px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(to right, #f9fbff, #ffffff);
    
    .chart-title {
      font-size: 18px;
      font-weight: bold;
      color: #1a1a1a;
      margin-bottom: 5px;
      position: relative;
      padding-left: 15px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: #3366ff;
        border-radius: 2px;
      }
    }
    
    .chart-subtitle {
      font-size: 14px;
      color: #666;
      padding-left: 15px;
    }
  }
  
  .chart-container {
    padding: 15px;
    
    .no-data {
      height: 480px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 16px;
      
      i {
        font-size: 60px;
        margin-bottom: 15px;
        color: #e6a23c;
      }
      
      .no-data-message {
        text-align: center;
        max-width: 250px;
        line-height: 1.5;
        color: #606266;
      }
    }
  }
}
</style>
