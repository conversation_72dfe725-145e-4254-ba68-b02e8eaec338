<template>
    <div style="overflow-y: auto; height: 100vh; padding: 10px 5px; ">
        <!-- 染厂数据动态追踪 -->
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '20px' }">
            <BigTitle style="background-color: rgb(30,145,254);" title="染厂数据动态追踪(投染量)" />
            <div class="date">数据更新日期: {{ endTime }}</div>
            <el-row :gutter="20" class="section-row">
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Order :small-title="{
                        firstTitle: '当日接单量(单位:吨)',
                        secondTitle: '当月接单量(单位:吨)',
                    }" :data="{
                        one: data.RzDayOrder,
                        two: data.RzMonthOrder,
                        three: 0,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="一、接单情况" titleColor="rgb(196,213,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Holding :small-title="{ firstTitle: '当前持单未染量(单位:吨)' }" :data="{
                        one: data.RzHoding,
                        two: data.RzIHoding,
                        three: data.RzIIHoding,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="二、持单情况" titleColor="rgb(196,213,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Production :data="{
                        one: data.RzDayProcution,
                        two: data.RzMonthProcution,
                        three: data.RzDayIProcution,
                        four: data.RzMonthIProcution,
                        five: data.RzDayIIProcution,
                        six: data.RzMonthIIProcution,
                    }" title="三、生产情况" :small-title="{
                        firstTitle: '当日投染量(单位:吨)',
                        secondTitle: '当月投染量(单位:吨)',
                    }" titleColor="rgb(196,213,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Shipment :data="{
                        one: data.RzDayShipment,
                        two: data.RzMonthShipment,
                        three: 0,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="四、出货情况" titleColor="rgb(196,213,221)" />
                </el-col>
            </el-row>
        </el-card>

        <!-- 贴合厂数据动态追踪 -->
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '20px' }">
            <BigTitle style="background-color: rgb(113,174,71);" title="贴合厂数据动态追踪(过机量)" />
            <div class="date">数据更新日期: {{ endTime }}</div>
            <el-row :gutter="20" class="section-row">
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Order :small-title="{
                        firstTitle: '当日接单量(单位:万米)',
                        secondTitle: '当月接单量(单位:万米)',
                    }" :data="{
                        one: data.ThDayOrder,
                        two: data.ThMonthOrder,
                        three: 0,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="一、接单情况" titleColor="rgb(229,241,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Holding :small-title="{ firstTitle: '当前持单量(单位:万米)' }" :data="{
                        one: data.ThHoding,
                        two: data.ThIHoding,
                        three: data.ThIIHoding,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="二、持单情况" titleColor="rgb(229,241,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Production :data="{
                        one: data.ThDayProcution,
                        two: data.ThMonthProcution,
                        three: data.ThDayIProcution,
                        four: data.ThMonthIProcution,
                        five: data.ThDayIIProcution,
                        six: data.ThMonthIIProcution,
                    }" title="三、生产情况" :small-title="{
                        firstTitle: '当日过机量(单位:万米)',
                        secondTitle: '当月过机量(单位:万米)',
                    }" titleColor="rgb(229,241,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Shipment :small-title="{
                        firstTitle: '当日出货(单位:万米)',
                        secondTitle: '当月出货(单位:万米)',
                    }" :data="{
                        one: data.ThDayShipment,
                        two: data.ThMonthShipment,
                        three: 0,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="四、出货情况" titleColor="rgb(229,241,221)" />
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <Th40Need :small-title="{ firstTitle: '40天需求量(万米)' }" :data="{
                        one: (data.Th402LNeed + data.Th403LNeed).toFixed(1),
                        two: data.Th402LNeed,
                        three: data.Th403LNeed,
                        four: 0,
                        five: 0,
                        six: 0,
                    }" title="五、40天需求量" titleColor="rgb(229,241,221)" />
                </el-col>
            </el-row>
        </el-card>

        <!-- 出货金额 -->
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '20px' }">
            <BigTitle style="background-color: rgb(207,154,206);" title="出货金额" />
            <el-row justify="center" :gutter="20" class="section-row">
                <el-col :xs="24" :sm="12" :md="12" :lg="6">
                    <ShipmentAmount :data="{
                        'one': totalAmount,
                        'two': RzAmount,
                        'three': ThAmount,
                        'four': PbAmount,
                        'five': 0,
                        'six': 0
                    }" titleColor="rgb(244,232,244)"></ShipmentAmount>
                </el-col>
            </el-row>
        </el-card>

        <div style="height: 50px;"></div>
        <div class="sticky-footer">
            <el-date-picker :editable="false" v-model="beginTime" type="date" placeholder="Pick a Date"
                format="YYYY/MM/DD" value-format="YYYY-MM-DD" />
            <el-date-picker :editable="false" v-model="endTime" type="date" placeholder="Pick a Date"
                format="YYYY/MM/DD" value-format="YYYY-MM-DD" />
            <el-button type="primary" @click="getData(beginTime, endTime)">查询</el-button>
        </div>
    </div>
</template>

<script setup lang="ts" name="productionData">
import { useRouter, useRoute } from 'vue-router';
import { DataPropsStand } from '@/component/dataCockpit/productionData/interface';
import { reactive, ref, Ref, computed, onMounted } from 'vue';
import { Order, Holding, Production, BigTitle, Shipment, ShipmentAmount, Th40Need } from '@/component/dataCockpit/productionData/index';
import { productionData, shipmentAmount } from '@/api/productionData';
import { ElMessage } from 'element-plus';
import { handleLogin } from '@/api/auth';

const router = useRouter();
const route = useRoute();

const beginTime = ref<string>(new Date(new Date().getFullYear(), new Date().getMonth(), 2).toISOString().split('T')[0]);
let tempEndTime = new Date(Date.now() - 86400000).toISOString().split('T')[0];
if (tempEndTime < beginTime.value) {
    tempEndTime = beginTime.value;
}
const endTime = ref<string>(tempEndTime);

const keys: (keyof DataPropsStand)[] = [
    'RzDayOrder', 'RzMonthOrder', 'RzIHoding', 'RzIIHoding', 'RzHoding',
    'RzDayIProcution', 'RzDayIIProcution', 'RzDayProcution', 'RzMonthIProcution',
    'RzMonthIIProcution', 'RzMonthProcution', 'RzDayShipment', 'RzMonthShipment',
    'ThDayOrder', 'ThMonthOrder', 'ThIHoding', 'ThIIHoding', 'ThHoding',
    'ThDayIProcution', 'ThDayIIProcution', 'ThDayProcution', 'ThMonthIProcution',
    'ThMonthIIProcution', 'ThMonthProcution', 'ThDayShipment', 'ThMonthShipment',
    'Th402LNeed', 'Th403LNeed'
];

const data: DataPropsStand = reactive(keys.reduce((acc, key) => {
    acc[key] = 0;
    return acc;
}, {} as DataPropsStand));

const ThAmount = ref<number>(0);
const RzAmount = ref<number>(0);
const PbAmount = ref<number>(0);

const totalAmount = computed(() => {
    const sum = RzAmount.value + ThAmount.value + PbAmount.value;
    return Math.round(sum * 10) / 10;
});


const fetchShipmentAmount = async (beginTime: string, endTime: string, type: string, targetRef: Ref<number>) => {
    try {
        const res = await shipmentAmount(beginTime, endTime, type);
        if (res.status === 200) {
            const rawAmount = res.data.response[0]?.['总出货金额'] || 0;
            const amountInWan = Math.round((rawAmount / 10000) * 10) / 10;
            targetRef.value = amountInWan;
        } else {
            ElMessage.error('请求错误: ' + res.data.msg);
        }
    } catch (error) {
        ElMessage.error('请求错误: ' + error);
    }
};

const getData = async (beginTime: string, endTime: string) => {
    try {
        await fetchShipmentAmount(beginTime, endTime, '贴合', ThAmount);
        await fetchShipmentAmount(beginTime, endTime, '染厂', RzAmount);
        await fetchShipmentAmount(beginTime, endTime, '胚布', PbAmount);

        const res = await productionData(beginTime, endTime);
        if (res.status === 200) {
            keys.forEach((key) => {
                data[key] = res.data.response[key] || 0;
            });
        } else {
            ElMessage.error('请求错误: ' + res.data.msg);
        }
    } catch (error) {
        ElMessage.error('请求错误: ' + error);
    }
};

onMounted(() => {
    const username = route.query.username || '';
    const password = route.query.password || '';
    if (!username || !password) {
        ElMessage.error('缺少用户名或密码参数');
        router.push({ name: 'login' });
        return;
    }

    handleLogin(username, password).then((val) => {
        if (val.data.success) {
            getData(beginTime.value, endTime.value);
        } else {
            ElMessage.error(val.data.msg || '登录失败');
            router.push({ name: 'login' });
        }
    }).catch((error) => {
        ElMessage.error('登录请求错误: ' + error);
        router.push({ name: 'login' });
    });
});
</script>

<style scoped>
.data-card {
    margin-bottom: 20px;
}

.section-row {
    margin-top: 10px;
}

.date {
    text-align: right;
    margin: 10px 0 20px 0;
    font-weight: 500;
    color: #666;
}

.sticky-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: white;
    /* 或者和页面背景一致 */
    padding: 10px 20px;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
}
</style>
